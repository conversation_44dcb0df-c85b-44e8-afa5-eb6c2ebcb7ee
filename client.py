#!/usr/bin/env python3
"""
Comprehensive test client for Jira MCP Server.
Tests all available tools with proper authentication.
"""

import asyncio
import json
import os

from mcp.client.session import ClientSession
from mcp.client.streamable_http import streamablehttp_client

# --- Test Data ---
TEST_PROJECT_KEY = os.environ.get("JIRA_PROJECT_KEY", "TEST")
TEST_ISSUE_SUMMARY = "MCP Test Issue"
TEST_ISSUE_DESCRIPTION = "This is a test issue created by the MCP Jira client."
TEST_ISSUE_TYPE = "Task"
TEST_JQL = "summary ~ 'MCP Test Issue'"

# Global variable to store created issue key for other tests
created_issue_key = None


async def get_auth_tokens():
    """
    Get authentication tokens from environment variables.
    """
    access_token = "XXXXX"
    refresh_token = "XXXXX"

    if not access_token or not refresh_token:
        print(
            "❌ Missing JIRA_ACCESS_TOKEN or JIRA_REFRESH_TOKEN environment variables"
        )
        print("   Please set these before running the test client.")
        print(
            "   You can obtain these by navigating to http://localhost:5001/authorize"
        )
        print("   Example:")
        print("   export JIRA_ACCESS_TOKEN='your_access_token_here'")
        print("   export JIRA_REFRESH_TOKEN='your_refresh_token_here'")
        return None, None

    return access_token, refresh_token


async def test_server_connectivity(session):
    """Test basic server connectivity and list available tools."""
    print("\n" + "=" * 50)
    print("🔧 Testing SERVER_CONNECTIVITY")
    print("=" * 50)

    try:
        tools = await session.list_tools()
        available_tools = [tool.name for tool in tools.tools]
        print(f"✅ Server connectivity successful")
        print(f"📚 Available tools: {available_tools}")
        return True
    except Exception as e:
        print(f"❌ Server connectivity failed: {e}")
        return False


async def test_create_issue(session):
    """Test the create_issue tool."""
    print("\n" + "=" * 50)
    print("📝 Testing CREATE_ISSUE")
    print("=" * 50)

    try:
        result = await session.call_tool(
            "create_issue",
            arguments={
                "summary": TEST_ISSUE_SUMMARY,
                "description": TEST_ISSUE_DESCRIPTION,
                "issueType": TEST_ISSUE_TYPE,
            },
        )
        print("✅ create_issue successful")
        print(result)
        print("=" * 50)
        response_data = json.loads(result.content[0].text)
        print("Response:", json.dumps(response_data, indent=2))

        global created_issue_key
        created_issue_key = response_data.get("key")
        print(f"📄 Created issue key: {created_issue_key}")

        return True
    except Exception as e:
        print(f"❌ create_issue failed: {e}")
        return False


async def test_get_issue(session):
    """Test the get_issue tool."""
    print("\n" + "=" * 50)
    print("🔍 Testing GET_ISSUE")
    print("=" * 50)

    if not created_issue_key:
        print("⚠️  Skipping get_issue test - no issue key available")
        return False

    try:
        result = await session.call_tool(
            "get_issue", arguments={"issueKey": created_issue_key}
        )
        print("✅ get_issue successful")
        response_data = json.loads(result.content[0].text)
        print("Response:", json.dumps(response_data, indent=2))
        return True
    except Exception as e:
        print(f"❌ get_issue failed: {e}")
        return False


async def test_search_issues(session):
    """Test the search_issues tool."""
    print("\n" + "=" * 50)
    print("🔍 Testing SEARCH_ISSUES")
    print("=" * 50)

    try:
        result = await session.call_tool(
            "search_issues",
            arguments={"projectKey": TEST_PROJECT_KEY, "jql": TEST_JQL},
        )
        print("✅ search_issues successful")
        response_data = json.loads(result.content[0].text)
        print("Response:", json.dumps(response_data, indent=2))
        return True
    except Exception as e:
        print(f"❌ search_issues failed: {e}")
        return False


async def test_delete_issue(session):
    """Test the delete_issue tool."""
    print("\n" + "=" * 50)
    print("🗑️  Testing DELETE_ISSUE")
    print("=" * 50)

    if not created_issue_key:
        print("⚠️  Skipping delete_issue test - no issue key available")
        return False

    try:
        result = await session.call_tool(
            "delete_issue", arguments={"issueKey": created_issue_key}
        )
        print("✅ delete_issue successful")
        response_data = json.loads(result.content[0].text)
        print("Response:", json.dumps(response_data, indent=2))
        return True
    except Exception as e:
        print(f"❌ delete_issue failed: {e}")
        return False


async def test_all_tools(server_url: str = "http://localhost:5001"):
    """Test all available tools in the Jira MCP server."""
    print("=" * 70)
    print("🚀 JIRA MCP SERVER - COMPREHENSIVE TOOL TEST")
    print("=" * 70)

    access_token, refresh_token = await get_auth_tokens()
    if not access_token or not refresh_token:
        return

    headers = {
        "x-access-token": access_token,
        "x-refresh-token": refresh_token,
    }

    print(f"🔗 Connecting to server: {server_url}/mcp")
    print("🔐 Using OAuth authentication")

    try:
        async with streamablehttp_client(f"{server_url}/mcp", headers=headers) as (
            read,
            write,
            _,
        ):
            async with ClientSession(read, write) as session:
                await session.initialize()
                print("✅ Connection initialized")

                test_results = {}
                test_functions = [
                    ("server_connectivity", test_server_connectivity),
                    ("create_issue", test_create_issue),
                    ("get_issue", test_get_issue),
                    ("search_issues", test_search_issues),
                    ("delete_issue", test_delete_issue),
                ]

                for test_name, test_func in test_functions:
                    try:
                        result = await test_func(session)
                        test_results[test_name] = result
                    except Exception as e:
                        print(f"❌ {test_name} failed with exception: {e}")
                        test_results[test_name] = False

                print("\n" + "=" * 70)
                print("📊 TEST SUMMARY")
                print("=" * 70)

                passed_tests = sum(1 for result in test_results.values() if result)
                total_tests = len(test_results)

                for test_name, result in test_results.items():
                    status = "✅ PASS" if result else "❌ FAIL"
                    print(f"{status} {test_name}")

                print(f"\n🎯 Results: {passed_tests}/{total_tests} tests passed")

                if passed_tests == total_tests:
                    print(
                        "🎉 All tests passed! The Jira MCP server is working correctly."
                    )
                else:
                    print("⚠️  Some tests failed. Check the logs above for details.")

    except Exception as e:
        print(f"❌ Connection failed: {e}")
        print("\nTroubleshooting tips:")
        print("1. Make sure the MCP server is running on the specified port")
        print("2. Verify your Jira OAuth tokens are valid and have the correct scopes")
        print("3. Check that your environment variables are set correctly")


if __name__ == "__main__":
    asyncio.run(test_all_tools())
