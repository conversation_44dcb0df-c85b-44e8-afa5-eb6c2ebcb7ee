#!/usr/bin/env python3
"""
Debug script to test cloud ID retrieval functionality.
"""

import os
import sys
import logging

# Add src to path
sys.path.insert(0, 'src')

from utils.cloud_id_utils import get_cloud_id_from_api
from helper.jira_client import JiraClient
from helper.oauth_config import BYOAccessTokenOAuthConfig

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

def test_cloud_id_retrieval():
    """Test cloud ID retrieval with different scenarios."""
    
    # Test 1: Check if we have a token file
    token_file = "jira_token.json"
    if os.path.exists(token_file):
        import json
        try:
            with open(token_file, 'r') as f:
                token_data = json.load(f)
            
            access_token = token_data.get('access_token')
            if access_token:
                logger.info("Found access token in jira_token.json")
                
                # Test direct API call
                logger.info("Testing direct API call to get cloud ID...")
                cloud_id = get_cloud_id_from_api(access_token)
                if cloud_id:
                    logger.info(f"✅ Successfully retrieved cloud ID: {cloud_id}")
                else:
                    logger.error("❌ Failed to retrieve cloud ID from API")
                
                # Test through JiraClient
                logger.info("Testing through JiraClient...")
                oauth_config = BYOAccessTokenOAuthConfig(access_token)
                client = JiraClient(oauth_config)
                client_cloud_id = client.get_cloud_id()
                if client_cloud_id:
                    logger.info(f"✅ JiraClient retrieved cloud ID: {client_cloud_id}")
                else:
                    logger.error("❌ JiraClient failed to retrieve cloud ID")
                    
            else:
                logger.error("No access_token found in jira_token.json")
        except Exception as e:
            logger.error(f"Error reading token file: {e}")
    else:
        logger.warning("No jira_token.json file found")
    
    # Test 2: Check environment variables
    env_cloud_id = os.getenv('JIRA_CLOUD_ID')
    if env_cloud_id:
        logger.info(f"Found JIRA_CLOUD_ID in environment: {env_cloud_id}")
    else:
        logger.info("No JIRA_CLOUD_ID environment variable set")

if __name__ == "__main__":
    test_cloud_id_retrieval()
