#!/usr/bin/env python3
"""
Test script for the enhanced Jira attachment methods.
This demonstrates all three attachment methods: local file, URL, and base64 content.
"""

import base64
import json
import os
import tempfile
from typing import Dict, Any

# Mock jira_fetcher for testing (replace with actual implementation)
class MockJiraFetcher:
    def post_attachment(self, issue_key: str, filepath: str = None, filename: str = None, 
                       file_content: bytes = None, content_type: str = None):
        """Mock implementation for testing"""
        print(f"Mock: Attaching to {issue_key}")
        if filepath:
            print(f"  Method: Local file path - {filepath}")
            print(f"  Filename: {filename or os.path.basename(filepath)}")
        elif file_content:
            print(f"  Method: File content ({len(file_content)} bytes)")
            print(f"  Filename: {filename}")
        print(f"  Content-Type: {content_type}")
        
        # Mock response
        return [{
            "id": "12345",
            "filename": filename or (os.path.basename(filepath) if filepath else "unknown"),
            "size": len(file_content) if file_content else (os.path.getsize(filepath) if filepath else 0),
            "mimeType": content_type or "application/octet-stream",
            "created": "2024-01-01T12:00:00.000Z",
            "author": {"displayName": "Test User"}
        }]
    
    def post_attachment_from_url(self, issue_key: str, file_url: str, filename: str = None):
        """Mock implementation for URL attachment"""
        print(f"Mock: Attaching from URL to {issue_key}")
        print(f"  URL: {file_url}")
        print(f"  Filename: {filename}")
        
        # Mock response
        return [{
            "id": "12346",
            "filename": filename or "url_attachment",
            "size": 1024,  # Mock size
            "mimeType": "application/octet-stream",
            "created": "2024-01-01T12:00:00.000Z",
            "author": {"displayName": "Test User"}
        }]


def test_local_file_attachment():
    """Test Method 1: Local file path attachment"""
    print("\n=== Testing Local File Attachment ===")
    
    # Create a temporary test file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
        f.write("This is a test file for local attachment.")
        temp_filepath = f.name
    
    try:
        jira_fetcher = MockJiraFetcher()
        
        # Test the attachment
        result = jira_fetcher.post_attachment(
            issue_key="TEST-123",
            filepath=temp_filepath,
            content_type="text/plain"
        )
        
        print("✅ Local file attachment successful!")
        print(f"Result: {json.dumps(result[0], indent=2)}")
        
    finally:
        # Clean up
        os.unlink(temp_filepath)


def test_url_attachment():
    """Test Method 2: URL-based attachment"""
    print("\n=== Testing URL Attachment ===")
    
    jira_fetcher = MockJiraFetcher()
    
    # Test URL attachment
    result = jira_fetcher.post_attachment_from_url(
        issue_key="TEST-123",
        file_url="https://example.com/sample-document.pdf",
        filename="sample-document.pdf"
    )
    
    print("✅ URL attachment successful!")
    print(f"Result: {json.dumps(result[0], indent=2)}")


def test_base64_attachment():
    """Test Method 3: Base64 content attachment"""
    print("\n=== Testing Base64 Content Attachment ===")
    
    # Create sample content
    sample_content = "This is a test file for base64 attachment."
    file_bytes = sample_content.encode('utf-8')
    
    jira_fetcher = MockJiraFetcher()
    
    # Test base64 attachment
    result = jira_fetcher.post_attachment(
        issue_key="TEST-123",
        file_content=file_bytes,
        filename="base64-test.txt",
        content_type="text/plain"
    )
    
    print("✅ Base64 content attachment successful!")
    print(f"Result: {json.dumps(result[0], indent=2)}")


def test_attachment_tool_simulation():
    """Simulate how the AttachFileTool would work with different methods"""
    print("\n=== Testing AttachFileTool Simulation ===")
    
    # Simulate different tool calls
    test_cases = [
        {
            "name": "Local File Method",
            "arguments": {
                "issueKey": "TEST-123",
                "filepath": "/tmp/test-file.txt"
            }
        },
        {
            "name": "URL Method", 
            "arguments": {
                "issueKey": "TEST-123",
                "file_url": "https://example.com/document.pdf",
                "filename": "important-document.pdf"
            }
        },
        {
            "name": "Base64 Method",
            "arguments": {
                "issueKey": "TEST-123",
                "file_content_base64": base64.b64encode(b"Sample file content").decode(),
                "filename": "encoded-file.txt",
                "content_type": "text/plain"
            }
        }
    ]
    
    for test_case in test_cases:
        print(f"\n--- {test_case['name']} ---")
        print(f"Arguments: {json.dumps(test_case['arguments'], indent=2)}")
        
        # Simulate validation logic
        args = test_case['arguments']
        methods_provided = sum([
            bool(args.get('filepath')),
            bool(args.get('file_url')),
            bool(args.get('file_content_base64'))
        ])
        
        if methods_provided == 1:
            print("✅ Validation passed - exactly one method provided")
        else:
            print(f"❌ Validation failed - {methods_provided} methods provided")


def demonstrate_production_scenarios():
    """Demonstrate common production scenarios"""
    print("\n=== Production Scenarios ===")
    
    scenarios = [
        {
            "name": "Cloud Storage File",
            "description": "File stored in AWS S3/Google Cloud Storage",
            "method": "base64",
            "example": {
                "issueKey": "PROD-456",
                "file_content_base64": "base64_encoded_content_from_cloud_storage",
                "filename": "monthly-report.pdf",
                "content_type": "application/pdf"
            }
        },
        {
            "name": "Public CDN File",
            "description": "File accessible via public URL",
            "method": "url",
            "example": {
                "issueKey": "PROD-456", 
                "file_url": "https://cdn.company.com/reports/2024/january.pdf",
                "filename": "january-report.pdf"
            }
        },
        {
            "name": "Database BLOB",
            "description": "File content stored in database",
            "method": "base64",
            "example": {
                "issueKey": "PROD-456",
                "file_content_base64": "base64_encoded_blob_from_database",
                "filename": "user-upload.jpg",
                "content_type": "image/jpeg"
            }
        }
    ]
    
    for scenario in scenarios:
        print(f"\n--- {scenario['name']} ---")
        print(f"Description: {scenario['description']}")
        print(f"Method: {scenario['method']}")
        print(f"Example: {json.dumps(scenario['example'], indent=2)}")


if __name__ == "__main__":
    print("🚀 Testing Enhanced Jira Attachment Methods")
    print("=" * 50)
    
    # Run all tests
    test_local_file_attachment()
    test_url_attachment() 
    test_base64_attachment()
    test_attachment_tool_simulation()
    demonstrate_production_scenarios()
    
    print("\n" + "=" * 50)
    print("✅ All tests completed!")
    print("\n📖 See ATTACHMENT_METHODS.md for detailed documentation")
    print("🔧 Replace MockJiraFetcher with actual implementation for real testing")
