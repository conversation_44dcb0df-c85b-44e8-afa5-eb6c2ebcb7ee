"""
Authentication context management for Jira MCP.
Provides AuthContext, context variable management, and header extraction.
"""

from contextvars import ContextVar

# Context variable to store the current AuthContext per request
_current_auth_context: ContextVar["AuthContext"] = ContextVar(
    "auth_context", default=None
)


class AuthContext:
    def __init__(
        self, access_token: str = None, refresh_token: str = None, cloud_id: str = None
    ):
        # Strip 'Bearer ' prefix if present in access token
        if access_token and access_token.startswith("Bearer "):
            self.access_token = access_token[7:].strip()
        else:
            self.access_token = access_token

        # Strip 'Bearer ' prefix if present in refresh token
        if refresh_token and refresh_token.startswith("Bearer "):
            self.refresh_token = refresh_token[7:].strip()
        else:
            self.refresh_token = refresh_token

        self.cloud_id = cloud_id

    def has_access_token(self) -> bool:
        """
        Check if a valid access token is available.
        Returns True if the access token is a non-empty string.
        """
        return bool(
            self.access_token
            and isinstance(self.access_token, str)
            and len(self.access_token) > 0
        )

    def has_refresh_token(self) -> bool:
        """
        Check if a valid refresh token is available.
        Returns True if the refresh token is a non-empty string.
        """
        return bool(
            self.refresh_token
            and isinstance(self.refresh_token, str)
            and len(self.refresh_token) > 0
        )

    def __str__(self) -> str:
        """String representation without exposing token values"""
        return (
            f"AuthContext(access_token={'Present' if self.has_access_token() else 'Missing'}, "
            f"refresh_token={'Present' if self.has_refresh_token() else 'Missing'}, "
            f"cloud_id={'Present' if self.cloud_id else 'Missing'})"
        )


def set_current_auth_context(auth_context: "AuthContext"):
    _current_auth_context.set(auth_context)


def get_current_auth_context() -> "AuthContext":
    return _current_auth_context.get()


def create_auth_context_from_headers(headers: dict) -> AuthContext:
    """
    Extracts access and refresh tokens from headers (dict of bytes->bytes).

    Args:
        headers: Dictionary of request headers (bytes keys/values)

    Returns:
        AuthContext instance with extracted tokens
    """
    import logging

    access_token = headers.get(b"authorization", b"").decode().strip()
    refresh_token = headers.get(b"x-refresh-token", b"").decode().strip()
    cloud_id = headers.get(b"x-atlassian-cloud-id", b"").decode().strip() or None

    # Log token presence without exposing values
    logging.debug(
        f"Creating auth context - Authorization header: {'Present' if access_token else 'Missing'}, "
        f"X-Refresh-Token header: {'Present' if refresh_token else 'Missing'}, "
        f"X-Atlassian-Cloud-ID header: {'Present' if cloud_id else 'Missing'}"
    )

    return AuthContext(
        access_token=access_token, refresh_token=refresh_token, cloud_id=cloud_id
    )
