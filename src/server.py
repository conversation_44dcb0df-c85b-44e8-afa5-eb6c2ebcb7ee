import asyncio
import contextlib
import json
import os

import mcp.types as types
import uvicorn
from event_store import InMemoryEventStore
from helper.config import HOST, PORT
from helper.logger import logger
from helper.scopes import ALL_SCOPES
from mcp.server import Server
from mcp.server.streamable_http_manager import StreamableHTTPSessionManager
from middleware.auth import (
    AuthMiddleware,
    extract_auth_from_mcp_request,
    send_oauth2_401_response,
)
from services.tools import get_confluence_tools, get_jira_tools

TOOLS = get_jira_tools() + get_confluence_tools()
from starlette.applications import Starlette
from starlette.middleware import Middleware
from starlette.middleware.cors import CORSMiddleware
from starlette.routing import Route

server = Server("jira-mcp")


@server.list_tools()
async def handle_list_tools() -> list[types.Tool]:
    return [tool.get_tool_definition() for tool in TOOLS]


@server.call_tool()
async def handle_call_tool(name: str, arguments: dict | None) -> types.CallToolResult:
    try:
        import inspect
        import json as _json

        from context.auth_context import get_current_auth_context
        from helper.jira_oauth import JiraOAuthSession

        def save_tokens_callback(token_data):
            # Save to your token file or context
            with open("jira_token.json", "w") as f:
                _json.dump(token_data, f, indent=2)

        auth_context = get_current_auth_context()

        # Get tokens from context
        try:
            access_token = auth_context.access_token
            refresh_token = auth_context.refresh_token

            # Check if access token and refresh token are available
            if not access_token or not refresh_token:
                error_msg = {
                    "error": {
                        "code": "AUTHENTICATION_REQUIRED",
                        "message": "Authentication required to access this resource",
                        "details": {
                            "authentication_requirements": [
                                {
                                    "provider": "jira",
                                    "auth_type": "bearer",
                                    "header_name": "Authorization",
                                    "header_format": "Bearer {access_token}",
                                    "required_scopes": ALL_SCOPES,  # optional
                                    "token_source": "access_token",
                                },
                                {
                                    "provider": "jira",
                                    "auth_type": "bearer",
                                    "header_name": "X-Refresh-Token",
                                    "header_format": "Bearer {refresh_token}",
                                    "token_source": "refresh_token",
                                    "required_scopes": ALL_SCOPES,  # optional
                                },
                            ],
                        },
                    }
                }
                if refresh_token:
                    error_msg = {
                        "error": {
                            "code": "AUTHENTICATION_REQUIRED",
                            "message": "Authentication required to access this resource",
                            "details": {
                                "authentication_requirements": [
                                    {
                                        "provider": "jira",
                                        "auth_type": "bearer",
                                        "header_name": "Authorization",
                                        "header_format": "Bearer {access_token}",
                                        "required_scopes": ALL_SCOPES,  # optional
                                        "token_source": "access_token",
                                    },
                                ],
                            },
                        }
                    }
                elif access_token:
                    error_msg = {
                        "error": {
                            "code": "AUTHENTICATION_REQUIRED",
                            "message": "Authentication required to access this resource",
                            "details": {
                                "authentication_requirements": [
                                    {
                                        "provider": "jira",
                                        "auth_type": "bearer",
                                        "header_name": "X-Refresh-Token",
                                        "header_format": "Bearer {refresh_token}",
                                        "token_source": "refresh_token",
                                        "required_scopes": ALL_SCOPES,
                                    }
                                ],
                            },
                        }
                    }
                raise Exception(error_msg)

        except LookupError:
            error_msg = {
                "error": {
                    "code": "AUTHENTICATION_REQUIRED",
                    "message": "Authentication required to access this resource",
                    "details": {
                        "authentication_requirements": [
                            {
                                "provider": "jira",
                                "auth_type": "bearer",
                                "header_name": "Authorization",
                                "header_format": "Bearer {access_token}",
                                "required_scopes": ALL_SCOPES,  # optional
                                "token_source": "access_token",
                            },
                            {
                                "provider": "jira",
                                "auth_type": "bearer",
                                "header_name": "X-Refresh-Token",
                                "header_format": "Bearer {refresh_token}",
                                "token_source": "refresh_token",
                                "required_scopes": ALL_SCOPES,  # optional
                            },
                        ],
                    },
                }
            }
            raise Exception(error_msg)
        # Prepare context objects
        # Always use a session that can auto-refresh tokens
        jira_session = JiraOAuthSession(
            access_token=auth_context.access_token,
            refresh_token=auth_context.refresh_token,
            client_id=getattr(auth_context, "client_id", None)
            or os.getenv("JIRA_CLIENT_ID"),
            client_secret=getattr(auth_context, "client_secret", None)
            or os.getenv("JIRA_CLIENT_SECRET"),
            token_updater=save_tokens_callback,
        )
        from helper.jira_fetcher import create_jira_fetcher_from_auth_context

        jira_fetcher = create_jira_fetcher_from_auth_context(jira_session=jira_session)

        # Get cloud ID if not already in auth context
        if not auth_context.cloud_id:
            auth_context.cloud_id = jira_fetcher.get_cloud_id()

        # Tool execution logic: pass only accepted context params
        for tool in TOOLS:
            if tool.get_tool_definition().name == name:
                execute_sig = inspect.signature(tool.execute)
                params = execute_sig.parameters
                kwargs = {}
                if "auth_context" in params:
                    kwargs["auth_context"] = auth_context
                if "jira_fetcher" in params:
                    kwargs["jira_fetcher"] = jira_fetcher
                if "jira_session" in params:
                    # For backward compatibility
                    kwargs["jira_session"] = jira_fetcher
                if "cloudid" in params:
                    kwargs["cloudid"] = auth_context.cloud_id
                result = await tool.execute(arguments or {}, **kwargs)
                # If the tool returns a TextContent or list of them, return as is
                if isinstance(result, types.TextContent):
                    return [result]
                if isinstance(result, list) and all(
                    isinstance(r, types.TextContent) for r in result
                ):
                    return result
                # Otherwise, wrap in TextContent
                return [
                    types.TextContent(
                        type="text",
                        text=json.dumps({"result": result}, indent=2),
                    )
                ]
        return [types.TextContent(type="text", text=f"Unknown tool: {name}")]
    except Exception as e:
        logger.error(f"Error calling tool {name}: {e}")
        raise e
        # error_obj = {
        #     "success": False,
        #     "error": f"Error calling tool: {e}",
        #     "is_error": True,
        # }
        # return [types.TextContent(type="text", text=json.dumps(error_obj, indent=2))]


async def create_app():
    event_store = InMemoryEventStore(max_events_per_stream=100)
    try:
        session_manager = StreamableHTTPSessionManager(
            app=server,
            event_store=event_store,
            json_response=False,
            stateless=True,
        )
        logger.info(
            "StreamableHTTPSessionManager initialized with authentication support"
        )
    except Exception as e:
        logger.error(f"Failed to initialize StreamableHTTPSessionManager: {e}")
        session_manager = None

    class HandleStreamableHttp:
        def __init__(self, session_manager):
            self.session_manager = session_manager

        async def __call__(self, scope, receive, send):
            if self.session_manager is not None:
                try:
                    logger.info("Handling Streamable HTTP connection ....")

                    # Extract authentication from MCP request (optional for list_tools)
                    auth_result = await extract_auth_from_mcp_request(scope, receive)

                    if not auth_result.get("authenticated", False):
                        # Authentication failed, but we'll continue for list_tools
                        # This allows list_tools to work without authentication
                        logger.debug(
                            "MCP request without authentication - continuing with empty context"
                        )
                        from context.auth_context import (
                            AuthContext,
                            set_current_auth_context,
                        )

                        empty_context = AuthContext()
                        set_current_auth_context(empty_context)
                    else:
                        logger.debug("MCP request authenticated successfully")

                    await self.session_manager.handle_request(scope, receive, send)
                    logger.info("Streamable HTTP connection closed ....")
                except Exception as e:
                    logger.error(f"Error handling Streamable HTTP request: {e}")
                    # Return proper OAuth2 401 response for any auth-related errors
                    try:
                        await send_oauth2_401_response(send)
                    except Exception:
                        await send(
                            {
                                "type": "http.response.start",
                                "status": 500,
                                "headers": [(b"content-type", b"application/json")],
                            }
                        )
                        await send(
                            {
                                "type": "http.response.body",
                                "body": json.dumps(
                                    {"error": "Internal server error"}
                                ).encode("utf-8"),
                            }
                        )
            else:
                await send(
                    {
                        "type": "http.response.start",
                        "status": 501,
                        "headers": [(b"content-type", b"application/json")],
                    }
                )
                await send(
                    {
                        "type": "http.response.body",
                        "body": json.dumps(
                            {"error": "Streamable HTTP transport is not available"}
                        ).encode("utf-8"),
                    }
                )

    routes = [
        Route(
            "/mcp",
            endpoint=HandleStreamableHttp(session_manager),
            methods=["GET", "POST"],
        ),
    ]

    middleware = [
        Middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_methods=["*"],
            allow_headers=["*"],
        ),
        Middleware(AuthMiddleware),
    ]

    @contextlib.asynccontextmanager
    async def lifespan(app):
        if session_manager is not None:
            async with session_manager.run():
                logger.info("Application started with StreamableHTTP session manager!")
                try:
                    yield
                finally:
                    logger.info("Application shutting down...")
        else:
            yield

    return Starlette(routes=routes, middleware=middleware, lifespan=lifespan)


async def start_server():
    app = await create_app()
    logger.info(f"Starting server at {HOST}:{PORT}")
    config = uvicorn.Config(app, host=HOST, port=PORT)
    server = uvicorn.Server(config)
    await server.serve()


if __name__ == "__main__":
    while True:
        try:
            asyncio.run(start_server())
        except KeyboardInterrupt:
            logger.info("Server stopped by user")
            break
        except Exception as e:
            logger.error(f"Server crashed with error: {e}")
            continue
