"""
Authentication middleware for extracting tokens from request headers.
Handles Authorization and X-Refresh-Token headers for MCP requests.
"""

import json
import logging
from typing import Any, Dict

# Import auth context related functions and classes
try:
    from context.auth_context import (
        AuthContext,
        create_auth_context_from_headers,
        set_current_auth_context,
    )
except ImportError:
    AuthContext = None
    create_auth_context_from_headers = None
    set_current_auth_context = None


class AuthMiddleware:
    """Middleware to extract and validate authentication headers."""

    def __init__(self, app):
        self.app = app

    async def __call__(self, scope, receive, send):
        """ASGI middleware interface."""
        if scope["type"] == "http":
            # Extract headers from scope
            headers = dict(scope.get("headers", []))

            # Create auth context from headers
            if create_auth_context_from_headers and set_current_auth_context:
                auth_context = create_auth_context_from_headers(headers)
                set_current_auth_context(auth_context)
                # Log authentication status (without exposing tokens)
                if (
                    hasattr(auth_context, "has_access_token")
                    and auth_context.has_access_token()
                ):
                    logging.debug("Request authenticated via Authorization header")
                else:
                    logging.debug("Request without Authorization header")
        # Continue with the request
        await self.app(scope, receive, send)


async def extract_auth_from_mcp_request(scope, receive) -> Dict[str, Any]:
    """
    Extract authentication headers specifically from MCP requests.
    This is used for MCP-specific request handling.

    Returns:
        Dict with authentication status and context information
    """
    try:
        # Extract headers from scope
        headers = dict(scope.get("headers", []))
        logging.debug(
            f"Extracting auth from MCP request with headers: {[k.decode() for k in headers.keys()]}"
        )

        # Create auth context from headers
        if create_auth_context_from_headers and set_current_auth_context:
            auth_context = create_auth_context_from_headers(headers)
            set_current_auth_context(auth_context)

            # Validate token format without logging the actual token
            has_access_token = auth_context.has_access_token()
            has_refresh_token = bool(auth_context.refresh_token)

            has_cloud_id = bool(auth_context.cloud_id)

            logging.info(
                f"Auth extraction complete - Access token: {'Valid' if has_access_token else 'Invalid'}, "
                f"Refresh token: {'Present' if has_refresh_token else 'Missing'}, "
                f"Cloud ID: {'Present' if has_cloud_id else 'Missing'}"
            )

            # Log cloud ID presence
            if has_cloud_id:
                logging.info(f"Cloud ID is present: {auth_context.cloud_id}")
            else:
                logging.warning(
                    "Cloud ID is missing. This may cause API calls to fail."
                )
                logging.info(
                    "To provide a cloud ID, include the X-Atlassian-Cloud-ID header in your request."
                )

            # For tools/list, we'll allow even without authentication
            # The actual tool execution will check for auth in handle_call_tool
            return {
                "authenticated": True,  # Always return true to allow listing tools
                "access_token": getattr(auth_context, "access_token", None),
                "refresh_token": getattr(auth_context, "refresh_token", None),
                "cloud_id": getattr(auth_context, "cloud_id", None),
                "context": auth_context,
            }
        else:
            logging.error("Auth context functions not implemented")
            return {
                "authenticated": False,
                "error": "Auth context not implemented",
                "context": None,
            }
    except Exception as error:
        logging.error(f"Error extracting auth from MCP request: {str(error)}")
        return {"authenticated": False, "error": str(error), "context": None}


from helper.scopes import ALL_SCOPES

async def send_oauth2_401_response(send):
    """
    Send a proper OAuth2 401 Unauthorized response with WWW-Authenticate header.
    This follows RFC 6750 and RFC 6749 specifications.

    The response includes information about how to authenticate properly with
    the Jira MCP server, including required scopes and authorization endpoint.
    """
    logging.info("Sending 401 Unauthorized response with OAuth2 details")

    # Customize this for your Jira instance
    jira_url = "https://your-jira-instance.atlassian.net"

    www_authenticate = (
        'Bearer realm="Jira MCP", '
        f'resource="{jira_url}", '
        'authorization="https://auth.atlassian.com/authorize", '
        f'scopes="{" ".join(ALL_SCOPES)}", '
        'bearer_methods_supported="header"'
    )

    headers = [
        (b"content-type", b"application/json"),
        (b"www-authenticate", www_authenticate.encode("utf-8")),
        (b"access-control-allow-origin", b"*"),  # Allow CORS
    ]

    error_response = {
        "error": "invalid_token",
        "error_description": "The access token provided is expired, revoked, malformed, or invalid",
        "message": "Authorization header missing or invalid",
        "is_error": True,
        "error_code": "UNAUTHORIZED",
        "help": "Please provide valid Authorization and X-Refresh-Token headers. Optionally include X-Atlassian-Cloud-ID header.",
        "authentication_requirements": [
            {
                "provider": "jira",
                "auth_type": "bearer",
                "header_name": "Authorization",
                "header_format": "Bearer {access_token}",
                "token_source": "access_token",
                "required_scopes": ALL_SCOPES,
            },
            {
                "provider": "jira",
                "auth_type": "bearer",
                "header_name": "X-Refresh-Token",
                "header_format": "Bearer {refresh_token}",
                "token_source": "refresh_token",
            },
        ],
    }

    await send(
        {
            "type": "http.response.start",
            "status": 401,
            "headers": headers,
        }
    )

    await send(
        {
            "type": "http.response.body",
            "body": json.dumps(error_response, indent=2).encode("utf-8"),
        }
    )


def validate_required_headers(headers: dict) -> tuple[bool, str]:
    """
    Validate that both required authentication headers are present and properly formatted.

    Args:
        headers: Dictionary of request headers (bytes keys/values)

    Returns:
        Tuple of (is_valid, error_message)
    """
    # Check for Authorization header
    auth_header = headers.get(b"authorization", b"").decode().strip()
    if not auth_header:
        return False, "Authorization header is missing"

    # Optionally validate Authorization header format (Bearer token)
    if not auth_header.startswith("Bearer "):
        return False, "Authorization header must use Bearer scheme"

    # Check for X-Refresh-Token header
    refresh_header = headers.get(b"x-refresh-token", b"").decode().strip()
    if not refresh_header:
        return False, "X-Refresh-Token header is missing"

    # Check for X-Atlassian-Cloud-ID header
    cloud_id = headers.get(b"x-atlassian-cloud-id", b"").decode().strip()
    if not cloud_id:
        logging.warning(
            "X-Atlassian-Cloud-ID header is missing. This is recommended for Jira Cloud API calls."
        )
        # This is a warning, not an error, as the header is optional
    else:
        logging.info(f"X-Atlassian-Cloud-ID header is present")

    # Both required headers are present and properly formatted
    return True, ""
