import json
from typing import Dict, List, Optional

from mcp.types import TextContent, Tool

from .base import BaseConfluenceTool


class GetSpacesTool(BaseConfluenceTool):
    def get_tool_definition(self) -> Tool:
        return Tool(
            name="get_confluence_spaces",
            description="Get all Confluence spaces.",
            inputSchema={
                "type": "object",
                "properties": {
                    "limit": {
                        "type": "integer",
                        "description": "Maximum number of spaces to return",
                        "default": 30,
                    }
                },
            },
        )

    async def execute(
        self, arguments: Dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        limit = arguments.get("limit", 30)
        if not cloudid:
            return [
                TextContent(
                    type="text", text="Missing cloudid for Confluence API call."
                )
            ]
        url = f"https://api.atlassian.com/ex/confluence/{cloudid}/wiki/api/v2/spaces?limit={limit}"
        response = jira_fetcher.get(url)
        if response.status_code == 200:
            return [TextContent(type="text", text=json.dumps(response.json()))]
        else:
            return [
                TextContent(
                    type="text",
                    text=f"Error fetching spaces: {response.status_code} - {response.text}",
                )
            ]
