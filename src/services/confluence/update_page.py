import json
from typing import Dict, List, Optional

from mcp.types import TextContent, Tool

from .base import BaseConfluenceTool


class UpdatePageTool(BaseConfluenceTool):
    def get_tool_definition(self) -> Tool:
        return Tool(
            name="confluence_update_page",
            description="Update the content or metadata of an existing Confluence page.",
            inputSchema={
                "type": "object",
                "properties": {
                    "pageId": {
                        "type": "string",
                        "description": "Confluence page ID",
                    },
                    "title": {
                        "type": "string",
                        "description": "New page title",
                    },
                    "body": {
                        "type": "string",
                        "description": "New page content in storage format",
                    },
                },
                "required": ["pageId"],
            },
        )

    async def execute(
        self, arguments: Dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        page_id = arguments.get("pageId")
        if not page_id:
            raise ValueError("pageId is required")

        if not cloudid:
            return [
                TextContent(
                    type="text", text="Missing cloudid for Confluence API call."
                )
            ]

        url = f"https://api.atlassian.com/ex/confluence/{cloudid}/wiki/api/v2/pages/{page_id}"

        # Get current page version
        response = jira_fetcher.get(url)
        if response.status_code != 200:
            return [
                TextContent(
                    type="text",
                    text=f"Error getting page version: {response.status_code} - {response.text}",
                )
            ]
        current_page_data = response.json()
        current_version = current_page_data["version"]["number"]

        payload = {
            "id": page_id,
            "status": "current",
            "title": arguments.get("title", current_page_data["title"]),
            "version": {"number": current_version + 1}
        }

        if arguments.get("body"):
            payload["body"] = {
                "representation": "storage",
                "value": arguments.get("body"),
            }

        response = jira_fetcher.put(url, json=payload)
        if response.status_code == 200:
            return [TextContent(type="text", text=json.dumps(response.json()))]
        else:
            return [
                TextContent(
                    type="text",
                    text=f"Error updating page: {response.status_code} - {response.text}",
                )
            ]
