import json
from typing import Dict, List, Optional

from mcp.types import Text<PERSON>ontent, Tool

from .base import BaseConfluenceTool


class SearchTool(BaseConfluenceTool):
    def get_tool_definition(self) -> Tool:
        return Tool(
            name="confluence_search",
            description="Search Confluence content using simple terms or CQL (Confluence Query Language).",
            inputSchema={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search query or CQL expression",
                    }
                },
                "required": ["query"],
            },
            examples=[
                {
                    "tool_name": "confluence_search",
                    "arguments": {"query": "text ~ 'release notes'"},
                    "thought": "The user is asking for release notes. I will use the `confluence_search` tool to search for pages with the text 'release notes'.",
                },
                {
                    "tool_name": "confluence_search",
                    "arguments": {"query": "space = 'DEV' and label = 'meeting-notes'"},
                    "thought": "The user wants to find meeting notes in the 'DEV' space. I will use the `confluence_search` tool with a CQL query to find pages with the 'meeting-notes' label in the 'DEV' space.",
                },
            ],
        )

    async def execute(
        self, arguments: Dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        query = arguments.get("query")
        if not query:
            raise ValueError("query is required")
        if not cloudid:
            return [
                TextContent(
                    type="text", text="Missing cloudid for Confluence API call."
                )
            ]
        url = f"https://api.atlassian.com/ex/confluence/{cloudid}/wiki/rest/api/search?cql={query}"
        response = jira_fetcher.get(url)
        if response.status_code == 200:
            return [TextContent(type="text", text=json.dumps(response.json()))]
        else:
            return [
                TextContent(
                    type="text",
                    text=f"Error searching Confluence: {response.status_code} - {response.text}",
                )
            ]
