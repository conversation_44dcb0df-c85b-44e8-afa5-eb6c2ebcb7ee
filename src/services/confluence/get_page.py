import json
from typing import Dict, List, Optional

from mcp.types import TextContent, Tool

from .base import BaseConfluenceTool


class GetPageTool(BaseConfluenceTool):
    def get_tool_definition(self) -> Tool:
        return Tool(
            name="get_confluence_page",
            description="Get a Confluence page by ID.",
            inputSchema={
                "type": "object",
                "properties": {
                    "pageId": {
                        "type": "string",
                        "description": "Confluence page ID",
                    }
                },
                "required": ["pageId"],
            },
        )

    async def execute(
        self, arguments: Dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        page_id = arguments.get("pageId")
        if not page_id:
            raise ValueError("pageId is required")
        if not cloudid:
            return [
                TextContent(
                    type="text", text="Missing cloudid for Confluence API call."
                )
            ]
        url = f"https://api.atlassian.com/ex/confluence/{cloudid}/wiki/api/v2/pages/{page_id}"
        response = jira_fetcher.get(url)
        if response.status_code == 200:
            return [TextContent(type="text", text=json.dumps(response.json()))]
        else:
            return [
                TextContent(
                    type="text",
                    text=f"Error fetching page: {response.status_code} - {response.text}",
                )
            ]
