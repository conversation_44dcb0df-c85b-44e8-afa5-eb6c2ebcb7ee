import json
from typing import Dict, List, Optional

from mcp.types import TextContent, Tool

from .base import BaseConfluenceTool


class CreatePageTool(BaseConfluenceTool):
    def get_tool_definition(self) -> Tool:
        return Tool(
            name="create_confluence_page",
            description="Create a new Confluence page.",
            inputSchema={
                "type": "object",
                "properties": {
                    "spaceId": {
                        "type": "string",
                        "description": "Confluence space ID. Either spaceId or spaceKey is required.",
                    },
                    "spaceKey": {
                        "type": "string",
                        "description": "Confluence space key. Either spaceId or spaceKey is required.",
                    },
                    "title": {
                        "type": "string",
                        "description": "Page title",
                    },
                    "body": {
                        "type": "string",
                        "description": "Page content in storage format",
                    },
                },
                "required": ["title", "body"],
            },
        )

    async def execute(
        self, arguments: Dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        space_id = arguments.get("spaceId")
        space_key = arguments.get("spaceKey")
        title = arguments.get("title")
        body = arguments.get("body")

        if not title or not body:
            raise ValueError("title and body are required")

        if not space_id and not space_key:
            raise ValueError("Either spaceId or spaceKey is required")

        if not cloudid:
            return [
                TextContent(
                    type="text", text="Missing cloudid for Confluence API call."
                )
            ]

        if space_key and not space_id:
            # Get space ID from space key
            url = f"https://api.atlassian.com/ex/confluence/{cloudid}/wiki/api/v2/spaces?keys={space_key}"
            response = jira_fetcher.get(url)
            if response.status_code == 200:
                data = response.json()
                results = data.get("results", [])
                if not results:
                    return [
                        TextContent(
                            type="text", text=f"No space found with key: {space_key}"
                        )
                    ]
                space_id = results[0]["id"]
            else:
                return [
                    TextContent(
                        type="text",
                        text=f"Error getting space ID: {response.status_code} - {response.text}",
                    )
                ]

        url = f"https://api.atlassian.com/ex/confluence/{cloudid}/wiki/api/v2/pages"
        payload = {
            "spaceId": space_id,
            "status": "current",
            "title": title,
            "body": {"representation": "storage", "value": body},
        }
        response = jira_fetcher.post(url, json=payload)
        if response.status_code == 200:
            return [TextContent(type="text", text=json.dumps(response.json()))]
        else:
            return [
                TextContent(
                    type="text",
                    text=f"Error creating page: {response.status_code} - {response.text}",
                )
            ]
