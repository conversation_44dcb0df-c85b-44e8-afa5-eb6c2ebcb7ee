import json
from typing import Dict, List, Optional

from mcp.types import TextContent, Tool

from .base import BaseConfluenceTool


class DeletePageTool(BaseConfluenceTool):
    def get_tool_definition(self) -> Tool:
        return Tool(
            name="confluence_delete_page",
            description="Delete a Confluence page permanently.",
            inputSchema={
                "type": "object",
                "properties": {
                    "pageId": {
                        "type": "string",
                        "description": "Confluence page ID",
                    }
                },
                "required": ["pageId"],
            },
        )

    async def execute(
        self, arguments: Dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        page_id = arguments.get("pageId")
        if not page_id:
            raise ValueError("pageId is required")
        if not cloudid:
            return [
                TextContent(
                    type="text", text="Missing cloudid for Confluence API call."
                )
            ]
        url = f"https://api.atlassian.com/ex/confluence/{cloudid}/wiki/api/v2/pages/{page_id}"
        response = jira_fetcher.delete(url)
        if response.status_code == 204:
            return [TextContent(type="text", text="Page deleted successfully.")]
        else:
            return [
                TextContent(
                    type="text",
                    text=f"Error deleting page: {response.status_code} - {response.text}",
                )
            ]
