from typing import List

from .base import BaseConfluenceTool
from .create_page import <PERSON><PERSON><PERSON><PERSON>Tool
from .delete_page import Delete<PERSON>ageTool
from .get_comments import GetCommentsTool
from .get_page import GetPageTool
from .get_page_ancestors import GetPageAncestorsTool
from .get_page_children import Get<PERSON><PERSON><PERSON><PERSON>drenTool
from .get_spaces import GetSpacesTool
from .search import SearchTool
from .update_page import UpdatePageTool


def get_confluence_tools() -> List[BaseConfluenceTool]:
    return [
        GetSpacesTool(),
        GetPageTool(),
        CreatePageTool(),
        SearchTool(),
        GetPageChildrenTool(),
        GetPageAncestorsTool(),
        GetCommentsTool(),
        UpdatePageTool(),
        DeletePageTool(),
    ]