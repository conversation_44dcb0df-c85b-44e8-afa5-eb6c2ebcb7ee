import json
import os
from typing import Dict, List, Optional

from mcp.types import Text<PERSON>ontent, Tool

from .base import BaseTool


class AddCommentWithAttachmentTool(BaseTool):
    """
    Adds a comment and a file attachment to a Jira issue by calling the
    correct, specialized methods on the Jira fetcher.
    """

    def get_tool_definition(self) -> Tool:
        # This part is correct and does not need to change.
        return Tool(
            name="add_comment_with_attachment",
            description="Add a comment with a file attachment to a Jira issue.",
            inputSchema={
                "type": "object",
                "properties": {
                    "issueKey": {"type": "string", "description": "Key of the issue."},
                    "comment": {
                        "type": "string",
                        "description": "The plain text content of the comment.",
                    },
                    "filename": {
                        "type": "string",
                        "description": "Name of the file to be attached.",
                    },
                    "filepath": {
                        "type": "string",
                        "description": "Local path to the file to attach.",
                    },
                },
                "required": ["issueKey", "comment", "filename", "filepath"],
            },
        )

    # This helper can be removed from this tool if it's available in a shared utility
    # or base class, but it's fine to keep it here for self-containment.
    def _create_adf_from_string(self, text_content: str) -> Optional[Dict]:
        """Creates a valid Atlassian Document Format (ADF) JSON object from plain text."""
        if not text_content:
            return None
        paragraphs = [
            {"type": "paragraph", "content": [{"type": "text", "text": p}]}
            for p in text_content.strip().split("\n")
            if p
        ]
        if not paragraphs:
            return None
        return {"type": "doc", "version": 1, "content": paragraphs}

    async def execute(
        self, arguments: dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        if not cloudid:
            raise RuntimeError("Missing cloudid for Jira Cloud API call.")

        issue_key = arguments.get("issueKey")
        filename = arguments.get("filename")
        filepath = arguments.get("filepath")
        comment_text = arguments.get("comment")

        if not all([issue_key, filename, filepath, comment_text]):
            raise ValueError(
                "issueKey, filename, filepath, and comment are all required."
            )

        # --- Step 1: Add the comment (using the generic post method for JSON) ---
        # This part uses the v3-compliant method from your JiraCommentMixin.
        comment_data = jira_fetcher.add_comment(issue_key, comment_text)
        comment_id = comment_data.get("id")
        print(f"DEBUG: Comment added successfully with ID: {comment_id}")

        # --- Step 2: Attach the file (using the SPECIALIZED post_attachment method) ---
        if not os.path.exists(filepath):
            raise ValueError(f"File not found at path: {filepath}")
        if os.path.getsize(filepath) > 100 * 1024 * 1024:
            raise ValueError("Attachment is too large (max 100MB)")

        print(f"DEBUG: Attaching file '{filename}' from path '{filepath}'...")

        # --- THIS IS THE CRITICAL FIX ---
        # We now call the dedicated `post_attachment` method from the
        # JiraAttachmentMixin, which is inherited by your jira_fetcher.
        # This method is specifically designed for multipart/form-data uploads.
        attachments = jira_fetcher.post_attachment(
            issue_key=issue_key, filepath=filepath, filename=filename
        )

        attachment_id = (
            attachments[0].get("id")
            if isinstance(attachments, list) and attachments
            else None
        )
        print(f"DEBUG: Attachment successful with ID: {attachment_id}")

        return [
            TextContent(
                type="text",
                text=json.dumps(
                    {
                        "message": "Comment and attachment added successfully",
                        "comment_id": comment_id,
                        "attachment_id": attachment_id,
                    }
                ),
            )
        ]
