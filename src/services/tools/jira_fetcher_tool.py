"""
Base tool class for Jira tools that use JiraFetcher.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

from helper.jira_fetcher import <PERSON>raFetcher
from mcp.types import TextContent, Tool


class JiraFetcherTool(ABC):
    """
    Base class for all Jira tools that use JiraFetcher.
    """

    @abstractmethod
    def get_tool_definition(self) -> Tool:
        """
        Get the tool definition for this tool.

        Returns:
            Tool definition
        """
        pass

    @abstractmethod
    async def execute(
        self, arguments: Dict[str, Any], jira_fetcher: JiraFetcher, **kwargs
    ) -> List[TextContent]:
        """
        Execute the tool with the given arguments.

        Args:
            arguments: Tool arguments
            jira_fetcher: JiraFetcher instance
            **kwargs: Additional arguments

        Returns:
            List of TextContent objects
        """
        pass
