import json
from functools import lru_cache
from typing import Dict, List, Optional

from mcp.types import TextContent, Tool

from .base import BaseTool


class CreateIssueTool(BaseTool):
    """
    Create a new Jira issue using the v3 API with summary, description, type, project, priority,
    story points, and sprint assignment.

    Example:
        {
            "summary": "API endpoint is slow",
            "description": "The /api/v1/data endpoint takes over 5 seconds to respond.",
            "issueType": "Bug",
            "projectKey": "API",
            "priority": "High",
            "assignee": "<EMAIL>",
            "storyPoints": 8,
            "sprintId": 123
        }
    """

    def get_tool_definition(self) -> Tool:
        return Tool(
            name="create_issue",
            description="Create a new Jira issue.",
            inputSchema={
                "type": "object",
                "properties": {
                    "summary": {
                        "type": "string",
                        "description": "A concise title for the issue.",
                    },
                    "description": {
                        "type": "string",
                        "description": "A detailed plain text description of the issue.",
                    },
                    "issueType": {
                        "type": "string",
                        "description": "Type of issue (e.g., 'Task').",
                    },
                    "projectKey": {
                        "type": "string",
                        "description": "Jira project key (e.g., 'TEST').",
                    },
                    "priority": {
                        "type": "string",
                        "description": "Priority of the issue (e.g., 'High', 'Medium', 'Low'). Case-insensitive.",
                    },
                    "assignee": {
                        "type": "string",
                        "description": "Email of the user to assign the issue to (optional).",
                    },
                    "storyPoints": {
                        "type": "number",
                        "description": "Optional story point estimate for the issue (e.g., 3, 5, 8).",
                    },
                    "sprintId": {
                        "type": "number",
                        "description": "Optional Sprint ID to add the issue to an active sprint.",
                    },
                },
                "required": ["summary", "issueType", "projectKey"],
            },
        )

    # --- HELPER METHODS ---

    async def _find_user_account_id(
        self, email: str, jira_fetcher, cloudid: str
    ) -> Optional[str]:
        url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/user/search?query={email}"
        response = jira_fetcher.get(url, headers={"Accept": "application/json"})
        if response.ok and response.json():
            for user in response.json():
                if user.get("emailAddress", "").lower() == email.lower():
                    return user.get("accountId")
        return None

    @lru_cache(maxsize=1)
    def _get_all_priorities(self, jira_fetcher, cloudid: str) -> List[Dict]:
        url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/priority"
        response = jira_fetcher.get(url, headers={"Accept": "application/json"})
        return response.json() if response.ok else []

    def _get_priority_id(
        self, priority_name: str, jira_fetcher, cloudid: str
    ) -> Optional[str]:
        if not priority_name:
            return None
        for p in self._get_all_priorities(jira_fetcher, cloudid):
            if p.get("name", "").lower() == priority_name.lower():
                return p.get("id")
        return None

    def _create_adf_from_string(self, text_content: str) -> Dict:
        if not text_content:
            return None
        paragraphs = [
            {"type": "paragraph", "content": [{"type": "text", "text": t}]}
            for t in text_content.strip().split("\n")
            if t
        ]
        return (
            {"type": "doc", "version": 1, "content": paragraphs} if paragraphs else None
        )

    async def execute(
        self, arguments: Dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        if not cloudid:
            raise RuntimeError("Missing cloudid for Jira Cloud API call.")

        warnings = {}
        payload = {
            "fields": {
                "project": {"key": arguments["projectKey"]},
                "summary": arguments["summary"],
                "issuetype": {"name": arguments["issueType"]},
            }
        }

        if "description" in arguments and arguments["description"]:
            adf_description = self._create_adf_from_string(arguments["description"])
            if adf_description:
                payload["fields"]["description"] = adf_description

        if arguments.get("priority"):
            pid = self._get_priority_id(arguments["priority"], jira_fetcher, cloudid)
            if pid:
                payload["fields"]["priority"] = {"id": pid}
            else:
                warnings["priority"] = (
                    f"Priority '{arguments['priority']}' not found. Using default priority."
                )

        if arguments.get("assignee"):
            aid = await self._find_user_account_id(
                arguments["assignee"], jira_fetcher, cloudid
            )
            if aid:
                payload["fields"]["assignee"] = {"accountId": aid}
            else:
                warnings["assignee"] = (
                    f"Assignee '{arguments['assignee']}' not found. Skipping assignment."
                )

        if "storyPoints" in arguments:
            try:
                if arguments["storyPoints"] > 50:
                    warnings["storyPoints"] = (
                        f"Story point value '{arguments['storyPoints']}' is unusually high."
                    )
                payload["fields"]["customfield_10016"] = arguments["storyPoints"]
            except Exception:
                warnings["storyPoints"] = (
                    "Story Points field could not be added. Possibly not configured for this project."
                )

        url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/issue"
        response = jira_fetcher.post(url, json=payload)

        if not response.ok:
            error_details = response.text
            try:
                error_json = response.json()
                errors = error_json.get("errorMessages", []) + list(
                    error_json.get("errors", {}).values()
                )
                error_details = " | ".join(map(str, errors))
            except json.JSONDecodeError:
                pass
            raise RuntimeError(
                f"Failed to create issue: {response.status_code} - {error_details}"
            )

        created = response.json()
        new_key = created.get("key")
        browse_url = (
            f"https://id.atlassian.com/browse/{new_key}"
            if new_key
            else "URL not available"
        )

        if arguments.get("sprintId") and new_key:
            sprint_url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/agile/1.0/sprint/{arguments['sprintId']}/issue"
            sprint_response = jira_fetcher.post(sprint_url, json={"issues": [new_key]})
            if not sprint_response.ok:
                warnings["sprint"] = (
                    f"Sprint ID '{arguments['sprintId']}' is invalid or issue could not be added."
                )

        result = {
            "key": new_key,
            "id": created.get("id"),
            "url": browse_url,
            "message": f"Successfully created issue {new_key}.",
        }
        if warnings:
            result["warnings"] = warnings

        return [TextContent(type="text", text=json.dumps(result, indent=2))]
