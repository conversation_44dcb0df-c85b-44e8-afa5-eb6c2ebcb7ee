import json
from typing import Dict, List, Optional

from mcp.types import <PERSON><PERSON>ontent, Tool

from .base import BaseTool


class CreateIssueLinkTool(BaseTool):
    """
    Creates a link between two Jira issues, such as 'blocks', 'is blocked by', or 'relates to'.
    """

    def get_tool_definition(self) -> Tool:
        return Tool(
            name="create_issue_link",
            description="Create a link between two existing Jira issues.",
            inputSchema={
                "type": "object",
                "properties": {
                    "inwardIssueKey": {
                        "type": "string",
                        "description": "The key of the issue that the link points TO. For a 'Blocks' link, this is the issue that IS BLOCKED.",
                    },
                    "outwardIssueKey": {
                        "type": "string",
                        "description": "The key of the issue that the link points FROM. For a 'Blocks' link, this is the issue that IS BLOCKING.",
                    },
                    "linkType": {
                        "type": "string",
                        "description": "The name of the link type (e.g., 'Blocks', 'Relates', 'Duplicates'). This is case-sensitive and must match a valid link type in your Jira instance.",
                    },
                },
                "required": ["inwardIssueKey", "outwardIssueKey", "linkType"],
            },
        )

    async def execute(
        self, arguments: Dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        if not cloudid:
            raise RuntimeError("Missing cloudid for Jira Cloud API call.")

        inward_issue_key = arguments.get("inwardIssueKey")
        outward_issue_key = arguments.get("outwardIssueKey")
        link_type_name = arguments.get("linkType")

        if not all([inward_issue_key, outward_issue_key, link_type_name]):
            raise ValueError(
                "inwardIssueKey, outwardIssueKey, and linkType are all required."
            )

        url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/issueLink"
        headers = {"Accept": "application/json", "Content-Type": "application/json"}

        payload = {
            "type": {"name": link_type_name},
            "inwardIssue": {"key": inward_issue_key},
            "outwardIssue": {"key": outward_issue_key},
        }

        print(f"DEBUG: Creating issue link with payload: {json.dumps(payload)}")
        response = jira_fetcher.post(url, headers=headers, json=payload)

        # A successful link creation returns a 201 Created status.
        if not response.ok:
            error_details = response.text
            try:
                error_json = response.json()
                errors = error_json.get("errorMessages", []) + list(
                    error_json.get("errors", {}).values()
                )
                error_details = " | ".join(map(str, errors))
            except json.JSONDecodeError:
                pass
            raise RuntimeError(
                f"Failed to create issue link: {response.status_code} - {error_details}"
            )

        return [
            TextContent(
                type="text",
                text=json.dumps(
                    {
                        "message": "Issue link created successfully.",
                        "link": f"{outward_issue_key} {link_type_name} {inward_issue_key}",
                    }
                ),
            )
        ]
