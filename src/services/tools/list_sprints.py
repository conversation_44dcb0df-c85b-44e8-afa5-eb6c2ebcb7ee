import json
from typing import Dict, List, Optional

from mcp.types import TextContent, Tool

from .base import BaseTool


class ListSprintsTool(BaseTool):
    """
    List all sprints for a given Jira board.
    """

    def get_tool_definition(self) -> Tool:
        return Tool(
            name="list_sprints",
            description="List all sprints for a given Jira board.",
            inputSchema={
                "type": "object",
                "properties": {
                    "boardId": {
                        "type": "string",
                        "description": "The ID of the board to get sprints from.",
                    },
                },
                "required": ["boardId"],
            },
        )

    async def execute(
        self, arguments: Dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        if not cloudid:
            raise RuntimeError("Missing cloudid")
        board_id = arguments.get("boardId")
        if not board_id:
            raise ValueError("boardId is required")

        sprint_url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/agile/1.0/board/{board_id}/sprint"
        sprint_response = jira_fetcher.get(sprint_url)

        if not sprint_response.ok:
            raise RuntimeError(
                f"Failed to list sprints: {sprint_response.status_code} - {sprint_response.text}"
            )

        return [TextContent(type="text", text=json.dumps(sprint_response.json(), indent=2))]


def factory():
    """
    Factory function to create an instance of the ListSprintsTool.
    """
    return ListSprintsTool()
