import json
from typing import Dict, List, Optional

from mcp.types import Text<PERSON>ontent, Tool

from .base import BaseTool


class DeleteIssueTool(BaseTool):
    def get_tool_definition(self) -> Tool:
        return Tool(
            name="delete_issue",
            description="Delete a Jira issue or subtask",
            inputSchema={
                "type": "object",
                "properties": {
                    "issueKey": {
                        "type": "string",
                        "description": "Key of the issue to delete",
                    }
                },
                "required": ["issueKey"],
            },
        )

    async def execute(
        self, arguments: Dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        issue_key = arguments.get("issueKey")
        if not issue_key:
            raise ValueError("issueKey is required")
        if not cloudid:
            return [
                TextContent(
                    type="text", text="Missing cloudid for Jira Cloud API call."
                )
            ]
        url = (
            f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/2/issue/{issue_key}"
        )
        print("DEBUG: Delete issue URL:", url)
        response = jira_fetcher.delete(url)

        if response.status_code in (200, 204):
            return [
                TextContent(
                    type="text",
                    text=json.dumps(
                        {
                            "message": f"Issue {issue_key} deleted successfully",
                            "url": f"https://id.atlassian.com/browse/{issue_key}",
                        }
                    ),
                )
            ]
        else:
            return [
                TextContent(
                    type="text",
                    text=f"Error deleting issue: {response.status_code} - {response.text}",
                )
            ]
