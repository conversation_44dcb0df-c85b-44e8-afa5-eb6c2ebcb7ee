from typing import List

from .base import BaseTool
from .add_comment import AddCommentTool
from .add_comment_with_attachment import AddCommentWithAttachmentTool
from .attach_content import Attach<PERSON>ontentTool
from .attach_file import AttachFileTool
from .create_issue import CreateIssueTool
from .create_issue_link import CreateIssueLinkTool
from .delete_issue import DeleteIssueTool
from .get_issue import GetIssueTool
from .get_my_permissions import GetMyPermissionsTool
from .get_user import GetUserTool
from .list_issue import ListProjectsTool
from .list_transitions import ListTransitionsTool
from .search_issues import SearchIssuesTool
from .transition_issue import TransitionIssueTool
from .update_issue import UpdateIssueTool
from .update_start_end_date_time import UpdateStartEndDateTimeTool
from .add_worklog import AddWorklogTool
from .list_sprints import ListSprintsTool
from .update_sprint_goal import UpdateSprintGoalTool
from .list_boards import ListBoardsTool
from .get_all_sprints import GetAllSprintsTool
from .get_sprint_by_id import GetSprintByIdTool

def get_jira_tools() -> List[BaseTool]:
    return [
        CreateIssueTool(),
        GetIssueTool(),
        ListProjectsTool(),
        DeleteIssueTool(),
        SearchIssuesTool(),
        UpdateIssueTool(),
        AttachFileTool(),
        TransitionIssueTool(),
        ListTransitionsTool(),
        AddCommentTool(),
        AddCommentWithAttachmentTool(),
        AttachContentTool(),
        CreateIssueLinkTool(),
        GetUserTool(),
        GetMyPermissionsTool(),
        UpdateStartEndDateTimeTool(),
        AddWorklogTool(),
        ListSprintsTool(),
        UpdateSprintGoalTool(),
        ListBoardsTool(),
        GetAllSprintsTool(),
        GetSprintByIdTool(),
    ]