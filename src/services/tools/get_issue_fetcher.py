"""
Tool for getting a Jira issue using JiraFetcher.
"""

import json
from typing import Any, Dict, List, Optional

from mcp.types import TextContent, Tool

from .jira_fetcher_tool import JiraFetcherTool


class GetIssueFetcherTool(JiraFetcherTool):
    """
    Get a Jira issue by key using JiraFetcher.

    Example:
        {
            "issueKey": "TEST-1"
        }

    Field details:
        - issueKey: (str) The Jira issue key. Example: "TEST-1"
    """

    def get_tool_definition(self) -> Tool:
        return Tool(
            name="get_issue",
            description="Get a Jira issue by key.",
            inputSchema={
                "type": "object",
                "properties": {
                    "issueKey": {
                        "type": "string",
                        "description": "The Jira issue key (e.g., 'TEST-1').",
                    },
                },
                "required": ["issueKey"],
            },
        )

    async def execute(
        self, arguments: Dict[str, Any], jira_fetcher, **kwargs
    ) -> List[TextContent]:
        try:
            issue_key = arguments["issueKey"]
            result = jira_fetcher.get_issue(issue_key)

            return [
                TextContent(
                    type="text",
                    text=json.dumps(result, indent=2),
                )
            ]
        except Exception as e:
            return [
                TextContent(
                    type="text",
                    text=f"Error getting issue: {str(e)}",
                )
            ]
