"""
Tool for creating a Jira issue using JiraFetcher.
"""

import json
from typing import Any, Dict, List, Optional

from mcp.types import TextContent, Tool

from .jira_fetcher_tool import JiraFetcherTool


class CreateIssueFetcherTool(JiraFetcherTool):
    """
    Create a new Jira issue with optional assignee and priority using JiraFetcher.

    Example:
        {
            "summary": "Fix login bug",
            "description": "Users cannot log in with Google SSO.",
            "issueType": "Bug",
            "projectKey": "TEST",
            "assignee": "<EMAIL>",
            "priority": "High"
        }

    Field details:
        - summary: (str) A concise title for the issue. Example: "Fix login bug"
        - description: (str, optional) A detailed description. Example: "Users cannot log in with Google SSO."
        - issueType: (str) Type of issue. Example: "Bug", "Task", "Story"
        - projectKey: (str) Jira project key. Example: "TEST"
        - assignee: (str, optional) Email address of the assignee. Example: "<EMAIL>"
        - priority: (str, optional) Priority. Example: "High", "Medium", "Lowest"
    """

    def get_tool_definition(self) -> Tool:
        return Tool(
            name="create_issue",
            description="Create a new Jira issue with optional assignee and priority.",
            inputSchema={
                "type": "object",
                "properties": {
                    "summary": {
                        "type": "string",
                        "description": "A concise title for the issue.",
                    },
                    "description": {
                        "type": "string",
                        "description": "A detailed description of the issue.",
                    },
                    "issueType": {
                        "type": "string",
                        "description": "Type of issue (e.g., 'Task', 'Bug', 'Story').",
                    },
                    "projectKey": {
                        "type": "string",
                        "description": "Jira project key (e.g., 'TEST').",
                    },
                    "assignee": {
                        "type": "string",
                        "description": "The email address of the user to assign the issue to.",
                    },
                    "priority": {
                        "type": "string",
                        "description": "The priority of the issue (e.g., 'High', 'Medium', 'Lowest').",
                    },
                },
                "required": ["summary", "issueType", "projectKey"],
            },
        )

    async def execute(
        self, arguments: Dict[str, Any], jira_fetcher, **kwargs
    ) -> List[TextContent]:
        import logging

        try:
            # Log the arguments for debugging
            logging.info(f"Creating issue with arguments: {arguments}")

            # Validate required fields
            if not arguments.get("projectKey"):
                return [
                    TextContent(
                        type="text",
                        text="Error: Project key is required but was not provided",
                    )
                ]

            if not arguments.get("summary"):
                return [
                    TextContent(
                        type="text",
                        text="Error: Summary is required but was not provided",
                    )
                ]

            if not arguments.get("issueType"):
                return [
                    TextContent(
                        type="text",
                        text="Error: Issue type is required but was not provided",
                    )
                ]

            # Attempt to create the issue
            result = jira_fetcher.create_issue(
                project_key=arguments["projectKey"],
                summary=arguments["summary"],
                description=arguments.get("description"),
                issue_type=arguments["issueType"],
                assignee=arguments.get("assignee"),
                priority=arguments.get("priority"),
            )

            # Log success
            logging.info(f"Successfully created issue: {result.get('key')}")

            return [
                TextContent(
                    type="text",
                    text=json.dumps(result, indent=2),
                )
            ]
        except Exception as e:
            # Log the error with detailed information
            import traceback

            logging.error(f"Error creating issue: {str(e)}")
            logging.error(f"Traceback: {traceback.format_exc()}")

            # Provide a more detailed error message
            error_message = f"Error creating issue: {str(e)}"

            # Add suggestions for common errors
            if "400" in str(e):
                error_message += "\n\nPossible causes for 400 Bad Request error:\n"
                error_message += "1. Missing required fields in the request\n"
                error_message += "2. Invalid project key\n"
                error_message += "3. Invalid issue type name\n"
                error_message += "4. Invalid assignee email\n"
                error_message += "5. Invalid priority name\n"
                error_message += "6. Missing or invalid cloud ID\n"
                error_message += "\nPlease check the values provided and try again."

                # Check if cloud ID is available
                try:
                    cloud_id = jira_fetcher.get_cloud_id()
                    if not cloud_id:
                        error_message += "\n\nCloud ID is missing. This is required for Jira Cloud API calls."
                        error_message += "\nPlease provide a valid cloud ID using one of these methods:"
                        error_message += (
                            "\n1. Set the JIRA_CLOUD_ID environment variable"
                        )
                        error_message += "\n2. Include the X-Atlassian-Cloud-ID header in your request"
                        error_message += "\n3. Use the cloud_id parameter when creating the JiraFetcher instance"
                except Exception as cloud_error:
                    error_message += f"\n\nError checking cloud ID: {str(cloud_error)}"

            return [
                TextContent(
                    type="text",
                    text=error_message,
                )
            ]
