import json
from typing import Dict, List, Optional

from mcp.types import <PERSON><PERSON><PERSON><PERSON>, Tool

from .base import BaseTool
from .list_boards import ListBoardsTool
from .list_sprints import ListSprintsTool


class GetAllSprintsTool(BaseTool):
    """
    Get all sprints across all Jira boards accessible to the user.
    """

    def get_tool_definition(self) -> Tool:
        return Tool(
            name="get_all_sprints",
            description="Get all sprints across all Jira boards accessible to the user.",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": [],
            },
        )

    async def execute(
        self, arguments: Dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        if not cloudid:
            raise RuntimeError("Missing cloudid")

        all_sprints = []

        # 1. Get all boards
        list_boards_tool = ListBoardsTool()
        boards_response = await list_boards_tool.execute({}, jira_fetcher, cloudid)
        boards_data = json.loads(boards_response[0].text)
        boards = boards_data.get("values", [])

        # 2. Iterate through boards and get sprints for each
        list_sprints_tool = ListSprintsTool()
        for board in boards:
            board_id = board.get("id")
            if board_id:
                try:
                    sprints_response = await list_sprints_tool.execute(
                        {"boardId": str(board_id)}, jira_fetcher, cloudid
                    )
                    sprints_data = json.loads(sprints_response[0].text)
                    all_sprints.extend(sprints_data.get("values", []))
                except Exception as e:
                    # Log the error but continue with other boards
                    print(f"WARNING: Could not fetch sprints for board {board_id}: {e}")

        return [TextContent(type="text", text=json.dumps(all_sprints, indent=2))]


def factory():
    """
    Factory function to create an instance of the GetAllSprintsTool.
    """
    return GetAllSprintsTool()
