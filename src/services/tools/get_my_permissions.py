# src/tools/get_my_permissions.py

import json
from typing import List

from mcp.types import TextContent, Tool

from .base import BaseTool


class GetMyPermissionsTool(BaseTool):
    def get_tool_definition(self) -> Tool:
        return Tool(
            name="get_my_permissions",
            description="Checks the abilities of the current API key by listing the permissions of the user account that owns the key. This is how you check the 'scopes' or 'limits' of your API token.",
            inputSchema={
                "type": "object",
                "properties": {
                    "projectKey": {
                        "type": "string",
                        "description": "Optional. The key of a project to check permissions for (e.g., 'TEST'). If you omit this, it will only check global permissions (like 'JIRA Administrator').",
                    }
                },
                "required": [],
            },
        )

    async def execute(
        self, arguments: dict, jira_fetcher, cloudid
    ) -> List[TextContent]:
        import json

        project_key = arguments.get("projectKey")
        headers = {"Accept": "application/json"}

        # Step 1: Fetch all possible permissions
        permissions_url = (
            f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/permissions"
        )
        permissions_resp = jira_fetcher.get(permissions_url, headers=headers)
        if not permissions_resp.ok:
            return [
                TextContent(
                    type="text",
                    text=json.dumps(
                        {
                            "message": "Failed to fetch permissions list",
                            "status_code": permissions_resp.status_code,
                            "response": permissions_resp.text,
                        }
                    ),
                    isError=True,
                )
            ]
        all_permissions = permissions_resp.json().get("permissions", {})
        permission_keys = list(all_permissions.keys())
        permissions_to_check = ",".join(permission_keys)

        # Step 2: Call mypermissions with the required 'permissions' parameter
        url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/mypermissions"
        params = {"permissions": permissions_to_check}
        if project_key:
            params["projectKey"] = project_key

        response = jira_fetcher.get(url, headers=headers, params=params)
        if not response.ok:
            return [
                TextContent(
                    type="text",
                    text=json.dumps(
                        {
                            "message": "Failed to get permissions",
                            "status_code": response.status_code,
                            "response": response.text,
                        }
                    ),
                    isError=True,
                )
            ]

        permissions_data = response.json()
        my_granted_permissions = sorted(
            [
                perm_name
                for perm_name, perm_details in permissions_data.get(
                    "permissions", {}
                ).items()
                if perm_details.get("havePermission") is True
            ]
        )

        context = (
            f"in project '{project_key}'"
            if project_key
            else "globally (not specific to any project)"
        )

        result = {
            "message": f"Found {len(my_granted_permissions)} granted permissions for the API key's user {context}.",
            "context": f"Project: {project_key}" if project_key else "Global",
            "permissions": my_granted_permissions,
        }

        return [TextContent(type="text", text=json.dumps(result, indent=2))]
