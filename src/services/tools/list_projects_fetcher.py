"""
Tool for listing Jira projects using JiraFetcher.
"""

import json
from typing import Any, Dict, List, Optional

from mcp.types import TextContent, Tool

from .jira_fetcher_tool import JiraFetcherTool


class ListProjectsFetcherTool(JiraFetcherTool):
    """
    List all Jira projects accessible to the user using JiraFetcher.
    """

    def get_tool_definition(self) -> Tool:
        return Tool(
            name="list_projects",
            description="List all Jira projects accessible to the user.",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": [],
            },
        )

    async def execute(
        self, arguments: Dict[str, Any], jira_fetcher, **kwargs
    ) -> List[TextContent]:
        try:
            result = jira_fetcher.list_projects()

            return [
                TextContent(
                    type="text",
                    text=json.dumps(result, indent=2),
                )
            ]
        except Exception as e:
            return [
                TextContent(
                    type="text",
                    text=f"Error listing projects: {str(e)}",
                )
            ]
