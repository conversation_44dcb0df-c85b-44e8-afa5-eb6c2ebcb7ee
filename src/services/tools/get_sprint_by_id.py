import json
from typing import Dict, List, Optional

from mcp.types import TextContent, Tool

from .base import BaseTool


class GetSprintByIdTool(BaseTool):
    """
    Get details of a specific Jira sprint by its ID.
    """

    def get_tool_definition(self) -> Tool:
        return Tool(
            name="get_sprint_by_id",
            description="Get details of a specific Jira sprint by its ID.",
            inputSchema={
                "type": "object",
                "properties": {
                    "sprintId": {
                        "type": "string",
                        "description": "The ID of the sprint to retrieve.",
                    },
                },
                "required": ["sprintId"],
            },
        )

    async def execute(
        self, arguments: Dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        if not cloudid:
            raise RuntimeError("Missing cloudid")
        sprint_id = arguments.get("sprintId")
        if not sprint_id:
            raise ValueError("sprintId is required")

        sprint_url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/agile/1.0/sprint/{sprint_id}"
        sprint_response = jira_fetcher.get(sprint_url)

        if not sprint_response.ok:
            raise RuntimeError(
                f"Failed to retrieve sprint {sprint_id}: {sprint_response.status_code} - {sprint_response.text}"
            )

        return [TextContent(type="text", text=json.dumps(sprint_response.json(), indent=2))]


def factory():
    """
    Factory function to create an instance of the GetSprintByIdTool.
    """
    return GetSprintByIdTool()
