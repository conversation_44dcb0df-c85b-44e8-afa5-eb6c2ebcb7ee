import json
from typing import Dict, List, Optional

from mcp.types import TextContent, Tool

from .base import BaseTool


class GetIssueTool(BaseTool):
    def get_tool_definition(self) -> Tool:
        return Tool(
            name="get_issue",
            description="Get a Jira issue by key.",
            inputSchema={
                "type": "object",
                "properties": {
                    "issueKey": {
                        "type": "string",
                        "description": "Jira issue key (e.g., 'TEST-1')",
                    }
                },
                "required": ["issueKey"],
            },
        )

    async def execute(
        self, arguments: Dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        issue_key = arguments.get("issueKey")
        if not issue_key:
            raise ValueError("issueKey is required")
        if not cloudid:
            return [
                TextContent(
                    type="text", text="Missing cloudid for Jira Cloud API call."
                )
            ]
        url = (
            f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/issue/{issue_key}"
        )
        print("DEBUG: Get issue URL:", url)
        response = jira_fetcher.get(url)

        if response.status_code == 200:
            issue = response.json()
            result = {
                "key": issue.get("key"),
                "summary": issue.get("fields", {}).get("summary"),
                "description": issue.get("fields", {}).get("description"),
                "status": issue.get("fields", {}).get("status", {}).get("name"),
                "url": f"https://id.atlassian.com/browse/{issue.get('key')}",
            }
            return [TextContent(type="text", text=json.dumps(result))]
        else:
            return [
                TextContent(
                    type="text",
                    text=f"Error fetching issue: {response.status_code} - {response.text}",
                )
            ]
