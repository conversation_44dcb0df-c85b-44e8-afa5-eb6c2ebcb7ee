import json
from typing import Dict, List, Optional

from mcp.types import <PERSON><PERSON>onte<PERSON>, Tool

from .base import BaseTool


class UpdateStartEndDateTimeTool(BaseTool):
    """
    Update the start and end date of an existing Jira issue.
    """

    def get_tool_definition(self) -> Tool:
        return Tool(
            name="update_start_end_date_time",
            description="Update the start and end date of a Jira issue.",
            inputSchema={
                "type": "object",
                "properties": {
                    "issueKey": {
                        "type": "string",
                        "description": "Key of the issue to update (e.g., 'TEST-48').",
                    },
                    "startDate": {
                        "type": "string",
                        "description": "New start date in YYYY-MM-DD format (optional).",
                    },
                    "endDate": {
                        "type": "string",
                        "description": "New end date in YYYY-MM-DD format (optional).",
                    },
                },
                "required": ["issueKey"],
            },
        )

    async def execute(
        self, arguments: Dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        if not cloudid:
            raise RuntimeError("Missing cloudid")
        issue_key = arguments.get("issueKey")
        if not issue_key:
            raise ValueError("issueKey is required")

        update_payload = {"fields": {}}

        # NOTE: The custom field IDs for start and end date may be different for your Jira instance.
        # You may need to find the correct custom field IDs for your instance.
        # Common custom field IDs for start and end date are customfield_10032 and customfield_10033.
        if "startDate" in arguments:
            update_payload["fields"]["customfield_10032"] = arguments["startDate"]
        if "endDate" in arguments:
            update_payload["fields"]["customfield_10033"] = arguments["endDate"]

        if update_payload["fields"]:
            update_url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/issue/{issue_key}"
            update_response = jira_fetcher.put(update_url, json=update_payload)
            if not update_response.ok:
                raise RuntimeError(
                    f"Failed to update fields: {update_response.status_code} - {update_response.text}"
                )

        response = {
            "key": issue_key,
            "message": "Successfully updated start and end date.",
            "url": f"https://id.atlassian.com/browse/{issue_key}",
        }

        return [TextContent(type="text", text=json.dumps(response, indent=2))]


def factory():
    """
    Factory function to create an instance of the UpdateStartEndDateTimeTool.
    """
    return UpdateStartEndDateTimeTool()
