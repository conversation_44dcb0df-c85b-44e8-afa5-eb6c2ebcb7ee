import json
from functools import lru_cache
from typing import Dict, List, Optional

from mcp.types import Text<PERSON>ontent, Tool

from .base import BaseTool


class UpdateIssueTool(BaseTool):
    """
    Update an existing Jira issue's fields and/or transition its workflow status.

    This tool allows updating key issue attributes such as summary, description, priority, assignee,
    story points, and sprint assignment. It also supports transitioning the issue to a new status.
    Field updates are attempted first, followed by a status transition if provided.

    Warnings will be included in the response if certain fields could not be updated due to
    configuration issues (e.g., field not present on screen, invalid values, or user not found),
    but the update will proceed with valid values.

    Example input:
        {
            "issueKey": "TEST-123",
            "summary": "Update with final review notes",
            "description": "Final tweaks before closing.",
            "priority": "Medium",
            "assignee": "<EMAIL>",
            "storyPoints": 5,
            "sprintId": 201,
            "transitionTo": "Done"
        }

    Required:
        - issueKey: The unique key of the Jira issue to update (e.g., 'PROJ-101').

    Optional:
        - summary: New issue title.
        - description: New plain text description.
        - priority: Priority name (e.g., 'Low', 'High').
        - assignee: Email address of new assignee, or empty string to unassign.
        - storyPoints: Numeric story point estimate (must be configured on the Jira board).
        - sprintId: Sprint ID to assign the issue to (must be active).
        - transitionTo: Workflow status name to transition the issue to.
    """

    def get_tool_definition(self) -> Tool:
        return Tool(
            name="update_issue",
            description="Update fields and/or change the status of a Jira issue.",
            inputSchema={
                "type": "object",
                "properties": {
                    "issueKey": {
                        "type": "string",
                        "description": "Key of the issue (e.g., 'TEST-48').",
                    },
                    "summary": {
                        "type": "string",
                        "description": "New title (optional).",
                    },
                    "description": {
                        "type": "string",
                        "description": "New plain text description (optional).",
                    },
                    "priority": {
                        "type": "string",
                        "description": "New priority name (optional).",
                    },
                    "assignee": {
                        "type": "string",
                        "description": "Assignee email or '' to unassign (optional).",
                    },
                    "storyPoints": {
                        "type": "number",
                        "description": "New story point value (optional).",
                    },
                    "sprintId": {
                        "type": "number",
                        "description": "Sprint ID to assign issue (optional).",
                    },
                    "transitionTo": {
                        "type": "string",
                        "description": "Target status name (optional).",
                    },
                },
                "required": ["issueKey"],
            },
        )

    # --- HELPER METHODS ---
    async def _find_user_account_id(
        self, email: str, jira_fetcher, cloudid: str
    ) -> Optional[str]:
        if not email:
            return None
        url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/user/search?query={email}"
        response = jira_fetcher.get(url, headers={"Accept": "application/json"})
        if response.ok and response.json():
            for user in response.json():
                if user.get("emailAddress", "").lower() == email.lower():
                    return user.get("accountId")
        return None

    @lru_cache(maxsize=1)
    def _get_all_priorities(self, jira_fetcher, cloudid: str) -> List[Dict]:
        url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/priority"
        response = jira_fetcher.get(url, headers={"Accept": "application/json"})
        return response.json() if response.ok else []

    def _get_priority_id(
        self, priority_name: str, jira_fetcher, cloudid: str
    ) -> Optional[str]:
        if not priority_name:
            return None
        priorities = self._get_all_priorities(jira_fetcher, cloudid)
        for p in priorities:
            if p.get("name", "").lower() == priority_name.lower():
                return p.get("id")
        return None

    def _create_adf_from_string(self, text_content: str) -> Optional[Dict]:
        if not text_content:
            return None
        paragraphs = [
            {"type": "paragraph", "content": [{"type": "text", "text": p}]}
            for p in text_content.strip().split("\n")
            if p
        ]
        return (
            {"type": "doc", "version": 1, "content": paragraphs} if paragraphs else None
        )

    async def _get_transition_id(
        self, issue_key: str, to_status: str, jira_fetcher, cloudid: str
    ) -> Optional[str]:
        url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/issue/{issue_key}/transitions"
        response = jira_fetcher.get(url, headers={"Accept": "application/json"})
        if not response.ok:
            return None
        transitions = response.json().get("transitions", [])
        for t in transitions:
            if t.get("name", "").lower() == to_status.lower():
                return t.get("id")
        return None

    # --- EXECUTE METHOD ---
    async def execute(
        self, arguments: Dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        if not cloudid:
            raise RuntimeError("Missing cloudid")
        issue_key = arguments.get("issueKey")
        if not issue_key:
            raise ValueError("issueKey is required")

        warnings = {}
        fields_updated = False
        transition_performed = False

        update_payload = {"fields": {}}
        updatable_fields = [
            "summary",
            "description",
            "priority",
            "assignee",
            "storyPoints",
        ]

        if any(field in arguments for field in updatable_fields):
            if "summary" in arguments:
                update_payload["fields"]["summary"] = arguments["summary"]

            if "description" in arguments:
                adf = self._create_adf_from_string(arguments["description"])
                if adf:
                    update_payload["fields"]["description"] = adf

            if "priority" in arguments:
                pid = self._get_priority_id(
                    arguments["priority"], jira_fetcher, cloudid
                )
                if pid:
                    update_payload["fields"]["priority"] = {"id": pid}
                else:
                    warnings["priority"] = (
                        f"Priority '{arguments['priority']}' not found."
                    )

            if "assignee" in arguments:
                if arguments["assignee"] == "":
                    update_payload["fields"]["assignee"] = None
                else:
                    aid = await self._find_user_account_id(
                        arguments["assignee"], jira_fetcher, cloudid
                    )
                    if aid:
                        update_payload["fields"]["assignee"] = {"accountId": aid}
                    else:
                        warnings["assignee"] = (
                            f"Assignee '{arguments['assignee']}' not found."
                        )

            if "storyPoints" in arguments:
                story_points = arguments["storyPoints"]
                try:
                    if story_points > 50:
                        warnings["storyPoints"] = (
                            f"Story point value '{story_points}' is unusually high. Saved anyway."
                        )
                    update_payload["fields"][
                        "customfield_10016"
                    ] = story_points  # Replace with your actual field ID
                except Exception as e:
                    warnings["storyPoints"] = f"Could not update story points: {str(e)}"

            # Update fields if any were modified
            if update_payload["fields"]:
                update_url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/issue/{issue_key}"
                update_response = jira_fetcher.put(update_url, json=update_payload)
                if not update_response.ok:
                    raise RuntimeError(
                        f"Failed to update fields: {update_response.status_code} - {update_response.text}"
                    )
                fields_updated = True

        # --- Optional Sprint Assignment ---
        if "sprintId" in arguments:
            sprint_id = arguments["sprintId"]
            sprint_url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/agile/1.0/sprint/{sprint_id}/issue"
            sprint_payload = {"issues": [issue_key]}
            sprint_response = jira_fetcher.post(sprint_url, json=sprint_payload)
            if not sprint_response.ok:
                warnings["sprint"] = (
                    f"Sprint assignment skipped: Sprint ID '{sprint_id}' is invalid or issue could not be added."
                )

        # --- Transition status ---
        target_status = arguments.get("transitionTo")
        if target_status:
            transition_id = await self._get_transition_id(
                issue_key, target_status, jira_fetcher, cloudid
            )
            if transition_id:
                transition_url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/issue/{issue_key}/transitions"
                transition_response = jira_fetcher.post(
                    transition_url, json={"transition": {"id": transition_id}}
                )
                if not transition_response.ok:
                    raise RuntimeError(
                        f"Failed to transition issue: {transition_response.status_code} - {transition_response.text}"
                    )
                transition_performed = True
            else:
                warnings["transition"] = (
                    f"Transition to '{target_status}' is not allowed or does not exist."
                )

        # --- Construct Final Response ---
        if not fields_updated and not transition_performed and not warnings:
            message = "No actions performed. The provided fields may have been empty or invalid."
        else:
            parts = []
            if fields_updated:
                parts.append("Fields updated")
            if transition_performed:
                parts.append(f"Status changed to '{target_status}'")
            message = (
                f"Successfully processed issue {issue_key}: {', and '.join(parts)}."
            )

        response = {
            "key": issue_key,
            "url": f"https://id.atlassian.com/browse/{issue_key}",
            "message": message,
        }
        if warnings:
            response["warnings"] = warnings

        return [TextContent(type="text", text=json.dumps(response, indent=2))]
