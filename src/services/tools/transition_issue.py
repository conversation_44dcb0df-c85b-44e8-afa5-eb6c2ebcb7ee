# src/tools/transition_issue.py

import json
from typing import List

from mcp.types import TextContent, Tool

from .base import BaseTool


class TransitionIssueTool(BaseTool):
    def get_tool_definition(self) -> Tool:
        return Tool(
            name="transition_issue",
            description="Transitions a Jira issue to a new status (e.g., 'In Progress', 'Done').",
            inputSchema={
                "type": "object",
                "properties": {
                    "issueKey": {
                        "type": "string",
                        "description": "The key of the issue to transition (e.g., 'TEST-34').",
                    },
                    "statusName": {
                        "type": "string",
                        "description": "The name of the transition action. IMPORTANT: This is the name of the action button you click in the Jira UI. It is case-sensitive and must match exactly. Common examples: 'Done', 'In Progress', 'To Do', 'Backlog'. Check the issue in Jira to see the available actions from its current status.",
                    },
                },
                "required": ["issueKey", "statusName"],
            },
        )

    async def execute(
        self, arguments: dict, jira_fetcher, cloudid
    ) -> List[TextContent]:
        import json

        issue_key = arguments["issueKey"]
        status_name = arguments["statusName"]

        # check the status_name and do not restrict with case-sensitivity
        status_name = status_name.strip()
        if status_name.lower() == "in progress":
            status_name = "In Progress"
        elif status_name.lower() == "done":
            status_name = "Done"
        elif status_name.lower() == "to do":
            status_name = "To Do"
        elif status_name.lower() == "backlog":
            status_name = "Backlog"

        # Step 1: Get available transitions
        transitions_url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/issue/{issue_key}/transitions"
        headers = {"Accept": "application/json"}

        transitions_resp = jira_fetcher.get(transitions_url, headers=headers)
        if not transitions_resp.ok:
            return [
                TextContent(
                    type="text",
                    text=json.dumps(
                        {
                            "message": "Failed to fetch available transitions",
                            "status_code": transitions_resp.status_code,
                            "response": transitions_resp.text,
                        }
                    ),
                )
            ]

        transitions = transitions_resp.json().get("transitions", [])
        transition_id = None
        for t in transitions:
            if t.get("name") == status_name:
                transition_id = t.get("id")
                break

        if not transition_id:
            return [
                TextContent(
                    type="text",
                    text=json.dumps(
                        {
                            "message": f"Transition '{status_name}' not found for issue {issue_key}",
                            "available_transitions": [
                                t.get("name") for t in transitions
                            ],
                        }
                    ),
                )
            ]

        # Step 2: Perform the transition
        transition_url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/issue/{issue_key}/transitions"
        payload = {"transition": {"id": transition_id}}
        transition_resp = jira_fetcher.post(
            transition_url,
            headers={**headers, "Content-Type": "application/json"},
            json=payload,
        )

        if not transition_resp.ok:
            return [
                TextContent(
                    type="text",
                    text=json.dumps(
                        {
                            "message": "Failed to transition issue",
                            "status_code": transition_resp.status_code,
                            "response": transition_resp.text,
                        }
                    ),
                )
            ]

        response = {
            "message": f"Issue {issue_key} successfully transitioned to '{status_name}'.",
            "url": f"https://id.atlassian.com/browse/{issue_key}",
        }

        return [TextContent(type="text", text=json.dumps(response))]
