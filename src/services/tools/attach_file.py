import json
import mimetypes
import os
from typing import List, Optional
from urllib.parse import urlparse

from mcp.types import TextContent, Tool

from .base import BaseTool


class AttachFileTool(BaseTool):
    """
    File attachment tool that supports attaching a file from a local path or a URL.
    """

    def get_tool_definition(self) -> Tool:
        return Tool(
            name="attach_file",
            description="Attaches a file to an existing Jira issue from a local path or a URL.",
            inputSchema={
                "type": "object",
                "properties": {
                    "issueKey": {
                        "type": "string",
                        "description": "The key of the issue to attach the file to (e.g., 'TEST-34').",
                    },
                    "filepath": {
                        "type": "string",
                        "description": "Local file path of the file to attach. Optional if 'file_url' is provided.",
                    },
                    "file_url": {
                        "type": "string",
                        "description": "URL of the file to attach. Optional if 'filepath' is provided.",
                    },
                },
                "required": ["issueKey"],
                "oneOf": [
                    {"required": ["filepath"]},
                    {"required": ["file_url"]},
                ],
            },
        )

    async def execute(
        self, arguments: dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        if not cloudid:
            raise RuntimeError("Cloud ID context is missing.")

        issue_key = arguments.get("issueKey")
        if not issue_key:
            raise ValueError("issueKey is required.")

        filepath = arguments.get("filepath")
        file_url = arguments.get("file_url")

        if not filepath and not file_url:
            raise ValueError("Either 'filepath' or 'file_url' must be provided.")

        if filepath and file_url:
            raise ValueError("Provide either 'filepath' or 'file_url', not both.")

        try:
            method_used = ""
            attachments = None

            if filepath:
                if not os.path.exists(filepath):
                    raise FileNotFoundError(f"The file at path '{filepath}' was not found.")

                file_size = os.path.getsize(filepath)
                if file_size > 100 * 1024 * 1024:
                    raise ValueError("File size exceeds the 100MB limit.")

                filename = os.path.basename(filepath)
                content_type, _ = mimetypes.guess_type(filepath)
                if not content_type:
                    content_type = "application/octet-stream"

                print(f"DEBUG: Attaching local file '{filename}' to {issue_key}")
                attachments = jira_fetcher.post_attachment(
                    issue_key=issue_key,
                    filepath=filepath,
                    filename=filename,
                    content_type=content_type,
                )
                method_used = "filepath"

            elif file_url:
                parsed_url = urlparse(file_url)
                filename = os.path.basename(parsed_url.path) or "attachment"

                print(f"DEBUG: Attaching remote file '{filename}' from URL to {issue_key}")
                attachments = jira_fetcher.post_attachment_from_url(
                    issue_key=issue_key, file_url=file_url, filename=filename
                )
                method_used = "file_url"

            if isinstance(attachments, list) and attachments:
                attachment = attachments[0]
                result = {
                    "success": True,
                    "message": "File attached successfully.",
                    "attachment": {
                        "id": attachment.get("id"),
                        "filename": attachment.get("filename"),
                        "size_bytes": attachment.get("size"),
                        "content_type": attachment.get("mimeType"),
                        "created": attachment.get("created"),
                        "author": attachment.get("author", {}).get("displayName"),
                    },
                    "method_used": method_used,
                }
            else:
                result = {
                    "success": True,
                    "message": "Attachment API call succeeded, but no attachment info was returned.",
                    "method_used": method_used,
                }

        except Exception as e:
            result = {
                "success": False,
                "error": str(e),
                "method_attempted": "filepath" if filepath else "file_url",
            }

        return [TextContent(type="text", text=json.dumps(result, indent=2))]