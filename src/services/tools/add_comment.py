import json
from typing import Dict, List, Optional

from mcp.types import Text<PERSON>ontent, Tool

from .base import BaseTool


class AddCommentTool(BaseTool):
    """
    Add a comment to a Jira issue. The comment text is plain text.
        Example:
        {
            "issueKey": "TEST-123",
            "comment": "This is a comment. :) (y) (i)"
        }

    Field details:
        - issueKey: (str) Key of the issue to comment on. Example: "TEST-123"
        - comment: (str) Comment text content.
    """

    def get_tool_definition(self) -> Tool:
        return Tool(
            name="add_comment",
            description="Add a comment to a Jira issue.",
            inputSchema={
                "type": "object",
                "properties": {
                    "issueKey": {
                        "type": "string",
                        "description": "Key of the issue to comment on (e.g., 'TEST-123').",
                    },
                    "comment": {
                        "type": "string",
                        "description": "The plain text content of the comment.",
                    },
                },
                "required": ["issueKey", "comment"],
            },
        )

    def _create_adf_from_string(self, text_content: str) -> Optional[Dict]:
        """
        Creates a valid Atlassian Document Format (ADF) JSON object from plain text.
        """
        if not text_content:
            return None
        paragraphs = [
            {"type": "paragraph", "content": [{"type": "text", "text": p}]}
            for p in text_content.strip().split("\n")
            if p
        ]
        if not paragraphs:
            return None
        return {"type": "doc", "version": 1, "content": paragraphs}

    async def execute(
        self, arguments: dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        if not cloudid:
            raise RuntimeError("Missing cloudid for Jira Cloud API call.")

        issue_key = arguments.get("issueKey")
        comment_text = arguments.get("comment")

        if not issue_key or not comment_text:
            raise ValueError("issueKey and comment are required")

        # --- V3 FIX: Convert the comment string to ADF ---
        adf_body = self._create_adf_from_string(comment_text)
        if not adf_body:
            raise ValueError("Comment text cannot be empty.")

        url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/issue/{issue_key}/comment"
        headers = {"Accept": "application/json", "Content-Type": "application/json"}
        payload = {"body": adf_body}  # Use the ADF object here

        print(
            f"DEBUG: Adding comment to {issue_key} with ADF payload: {json.dumps(payload)}"
        )
        response = jira_fetcher.post(url, headers=headers, json=payload)

        if not response.ok:
            error_details = response.text
            try:
                error_json = response.json()
                error_details = error_json.get("errorMessages", []) + list(
                    error_json.get("errors", {}).values()
                )
                error_details = " | ".join(map(str, error_details))
            except json.JSONDecodeError:
                pass
            raise RuntimeError(
                f"Failed to add comment: {response.status_code} - {error_details}"
            )

        comment = response.json()
        browse_url = (
            comment.get("self", "").split("/rest/api/")[0]
            + f"/browse/{issue_key}?focusedCommentId={comment.get('id')}"
        )

        return [
            TextContent(
                type="text",
                text=json.dumps(
                    {
                        "message": "Comment added successfully",
                        "id": comment.get("id"),
                        "url": f"https://id.atlassian.com/browse/{issue_key}",
                    }
                ),
            )
        ]
