import json
from typing import Dict, List, Optional

from mcp.types import <PERSON><PERSON>ontent, Tool

from .base import BaseTool


class ListBoardsTool(BaseTool):
    """
    List all Jira boards accessible to the user.
    """

    def get_tool_definition(self) -> Tool:
        return Tool(
            name="list_boards",
            description="List all Jira boards accessible to the user.",
            inputSchema={
                "type": "object",
                "properties": {},
                "required": [],
            },
        )

    async def execute(
        self, arguments: Dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        if not cloudid:
            raise RuntimeError("Missing cloudid")

        boards_url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/agile/1.0/board"
        boards_response = jira_fetcher.get(boards_url)

        if not boards_response.ok:
            raise RuntimeError(
                f"Failed to list boards: {boards_response.status_code} - {boards_response.text}"
            )

        return [TextContent(type="text", text=json.dumps(boards_response.json(), indent=2))]


def factory():
    """
    Factory function to create an instance of the ListBoardsTool.
    """
    return ListBoardsTool()
