import json
from typing import Dict, List, Optional

from mcp.types import TextContent, Tool

from .base import BaseTool


class SearchIssuesTool(BaseTool):
    """
    Search for issues in a project using JQL.

    Example:
        {
            "projectKey": "TEST",
            "jql": "status = 'To Do' AND priority = 'High'"
        }

    Field details:
        - projectKey: (str) Project key. Example: "TEST"
        - jql: (str) JQL filter statement. Example: "status = 'To Do' AND priority = 'High'"
    """

    def get_tool_definition(self) -> Tool:
        return Tool(
            name="search_issues",
            description="Search for issues in a project using JQL",
            inputSchema={
                "type": "object",
                "properties": {
                    "projectKey": {
                        "type": "string",
                        "description": 'Project key (e.g., "MRR")',
                    },
                    "jql": {
                        "type": "string",
                        "description": "JQL filter statement (e.g., status = 'To Do' AND priority = 'High')",
                    },
                },
                "required": ["projectKey", "jql"],
            },
        )

    async def execute(
        self, arguments: Dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        project_key = arguments.get("projectKey")
        jql = arguments.get("jql")
        if not project_key or not jql:
            raise ValueError("projectKey and jql are required")
        if not cloudid:
            return [
                TextContent(
                    type="text", text="Missing cloudid for Jira Cloud API call."
                )
            ]
        url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/search"
        full_jql = f"project = {project_key} AND {jql}"
        params = {
            "jql": full_jql,
            "maxResults": 100,
            "fields": "summary,description,status,priority,assignee,issuetype",
        }
        print("DEBUG: Search issues URL:", url)
        print("DEBUG: Search params:", params)
        response = jira_fetcher.get(url, params=params)

        if response.status_code == 200:
            data = response.json()
            issues = data.get("issues", [])
            results = [
                {
                    "key": issue.get("key"),
                    "summary": issue.get("fields", {}).get("summary"),
                    "status": issue.get("fields", {}).get("status", {}).get("name"),
                    "priority": (
                        issue.get("fields", {}).get("priority", {}).get("name")
                        if issue.get("fields", {}).get("priority")
                        else None
                    ),
                    "assignee": (
                        issue.get("fields", {}).get("assignee", {}).get("emailAddress")
                        if issue.get("fields", {}).get("assignee")
                        else None
                    ),
                    "type": issue.get("fields", {}).get("issuetype", {}).get("name"),
                    "url": f"https://id.atlassian.com/browse/{issue.get('key')}",
                }
                for issue in issues
            ]
            return [
                TextContent(
                    type="text",
                    text=json.dumps(results),
                )
            ]
        else:
            return [
                TextContent(
                    type="text",
                    text=f"Error searching issues: {response.status_code} - {response.text}",
                )
            ]
