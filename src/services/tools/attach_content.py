import base64
import json
import os
import tempfile
from typing import Dict, List, Optional

from mcp.types import Text<PERSON>ontent, Tool

from .base import BaseTool


class AttachContentTool(BaseTool):
    """
    Create and attach content directly to a Jira issue.
    Content can be plain text or base64 encoded for binary data.
    """

    def get_tool_definition(self) -> Tool:
        return Tool(
            name="attach_content",
            description="Creates a file from provided content and attaches it to a Jira issue.",
            inputSchema={
                "type": "object",
                "properties": {
                    "issueKey": {
                        "type": "string",
                        "description": "Key of the issue to attach the content to.",
                    },
                    "filename": {
                        "type": "string",
                        "description": "The name of the attachment file as it will appear in Jira.",
                    },
                    "content": {
                        "type": "string",
                        "description": "The content to include in the attachment.",
                    },
                    "encoding": {
                        "type": "string",
                        "description": "Encoding of the content. Use 'base64' for binary data, 'none' for plain text.",
                        "enum": ["none", "base64"],
                        "default": "none",
                    },
                },
                "required": ["issueKey", "filename", "content"],
            },
        )

    async def execute(
        self, arguments: dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        if not cloudid:
            raise RuntimeError("Cloud ID context is missing.")

        issue_key = arguments.get("issueKey")
        filename = arguments.get("filename")
        content = arguments.get("content")
        encoding = arguments.get("encoding", "none")

        if not all([issue_key, filename, content]):
            raise ValueError("issueKey, filename, and content are required.")

        # --- Step 1: Prepare the content bytes ---
        try:
            if encoding == "base64":
                content_bytes = base64.b64decode(content)
            else:
                content_bytes = content.encode("utf-8")
        except Exception as e:
            raise ValueError(
                f"Failed to process content with encoding '{encoding}': {e}"
            )

        if len(content_bytes) > 100 * 1024 * 1024:
            raise ValueError("Content size exceeds the 100MB limit for attachments.")

        # --- Step 2: Write to a temporary file and upload ---
        temp_file_path = None
        try:
            # Create a temporary file to hold the content
            with tempfile.NamedTemporaryFile(delete=False, mode="wb") as temp_file:
                temp_file.write(content_bytes)
                temp_file_path = temp_file.name

            print(f"DEBUG: Content written to temporary file: {temp_file_path}")

            # --- THIS IS THE FIX ---
            # Call the robust, dedicated attachment method on the fetcher
            attachments = jira_fetcher.post_attachment(
                issue_key=issue_key, filepath=temp_file_path, filename=filename
            )

            # --- Step 3: Process the response ---
            if isinstance(attachments, list) and attachments:
                attachment = attachments[0]
                result = {
                    "message": "Content attached successfully.",
                    "filename": attachment.get("filename"),
                    "attachment_id": attachment.get("id"),
                    "size_bytes": attachment.get("size"),
                }
            else:
                result = {
                    "message": "Attachment API call succeeded, but no attachment info was returned."
                }

            return [TextContent(type="text", text=json.dumps(result, indent=2))]

        finally:
            # --- Step 4: Clean up the temporary file ---
            if temp_file_path and os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
                print(f"DEBUG: Cleaned up temporary file: {temp_file_path}")
