from typing import List

from mcp.types import TextContent, Tool

from .base import BaseTool


class GetUserTool(BaseTool):
    """
    Get a user's account ID by their email address.
    """

    def get_tool_definition(self) -> Tool:
        return Tool(
            name="get_user",
            description="Get a user's account ID by email address",
            inputSchema={
                "type": "object",
                "properties": {
                    "email": {"type": "string", "description": "User's email address"}
                },
                "required": ["email"],
            },
        )

    async def execute(
        self, arguments: dict, jira_fetcher, cloudid
    ) -> List[TextContent]:
        import json

        email = arguments.get("email")
        if not email:
            raise ValueError("email is required")

        url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/user/search"
        headers = {"Accept": "application/json"}
        params = {"query": email}

        response = jira_fetcher.get(url, headers=headers, params=params)
        if not response.ok:
            return [
                TextContent(
                    type="text",
                    text=json.dumps(
                        {
                            "message": "Failed to search for user",
                            "status_code": response.status_code,
                            "response": response.text,
                        }
                    ),
                )
            ]

        users = response.json()
        if not users:
            return [
                TextContent(
                    type="text",
                    text=json.dumps({"message": f"No user found with email: {email}"}),
                )
            ]

        user = users[0]
        return [
            TextContent(
                type="text",
                text=json.dumps(
                    {
                        "accountId": user.get("accountId"),
                        "displayName": user.get("displayName"),
                        "emailAddress": user.get("emailAddress"),
                        "active": user.get("active"),
                    }
                ),
            )
        ]
