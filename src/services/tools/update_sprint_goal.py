import json
from typing import Dict, List, Optional

from mcp.types import TextContent, Tool

from .base import BaseTool


class UpdateSprintGoalTool(BaseTool):
    """
    Update the goal of a sprint.
    """

    def get_tool_definition(self) -> Tool:
        return Tool(
            name="update_sprint_goal",
            description="Update the goal of a sprint.",
            inputSchema={
                "type": "object",
                "properties": {
                    "sprintId": {
                        "type": "string",
                        "description": "The ID of the sprint to update.",
                    },
                    "goal": {
                        "type": "string",
                        "description": "The new goal for the sprint.",
                    },
                },
                "required": ["sprintId", "goal"],
            },
        )

    async def execute(
        self, arguments: Dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        if not cloudid:
            raise RuntimeError("Missing cloudid")
        sprint_id = arguments.get("sprintId")
        if not sprint_id:
            raise ValueError("sprintId is required")
        goal = arguments.get("goal")
        if not goal:
            raise ValueError("goal is required")

        update_url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/agile/1.0/sprint/{sprint_id}"
        update_payload = {"goal": goal}
        update_response = jira_fetcher.post(update_url, json=update_payload)

        if not update_response.ok:
            raise RuntimeError(
                f"Failed to update sprint goal: {update_response.status_code} - {update_response.text}"
            )

        response = {
            "id": sprint_id,
            "message": "Successfully updated sprint goal.",
        }

        return [TextContent(type="text", text=json.dumps(response, indent=2))]


def factory():
    """
    Factory function to create an instance of the UpdateSprintGoalTool.
    """
    return UpdateSprintGoalTool()
