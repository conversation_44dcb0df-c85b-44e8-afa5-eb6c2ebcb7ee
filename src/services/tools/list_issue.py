# import json
# from typing import Any, Dict, List

# from mcp.types import TextContent, Tool

# from .base import BaseTool


# class ListProjectsTool(BaseTool):
#     def get_tool_definition(self) -> Tool:
#         return Tool(
#             name="list_issue",
#             description="List all Jira projects accessible to the user.",
#             inputSchema={"type": "object", "properties": {}},
#         )

#     async def execute(self, arguments: dict, jira_client) -> List[TextContent]:

#         projects = jira_client.projects()
#         result = [{"key": p.key, "name": p.name} for p in projects]
#         return [TextContent(type="text", text=json.dumps(result))]


# AFTER
import json
from typing import List

from mcp.types import TextContent, Tool

from .base import BaseTool


class ListProjectsTool(BaseTool):
    def get_tool_definition(self) -> Tool:
        return Tool(
            name="list_projects",  # Changed name for clarity, was 'list_issue'
            description="List all Jira projects accessible to the user.",
            inputSchema={"type": "object", "properties": {}},
        )

    # 1. FIX THE SIGNATURE: Add 'jira_client' as an accepted argument.
    async def execute(
        self, arguments: dict, jira_fetcher, cloudid
    ) -> List[TextContent]:
        import json

        url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/project"
        headers = {"Accept": "application/json"}

        response = jira_fetcher.get(url, headers=headers)
        if not response.ok:
            return [
                TextContent(
                    type="text",
                    text=json.dumps(
                        {
                            "message": "Failed to list projects",
                            "status_code": response.status_code,
                            "response": response.text,
                        }
                    ),
                )
            ]

        projects = response.json()
        result = [{"key": p.get("key"), "name": p.get("name")} for p in projects]

        return [TextContent(type="text", text=json.dumps(result, indent=2))]
