import json
from typing import List, Optional
from urllib.parse import urlparse

from mcp.types import TextContent, Tool

from .base import BaseTool


class AttachFileFromUrlTool(BaseTool):
    """
    Attach a file from a remote URL to an existing Jira issue.
    Perfect for production environments where local file paths don't exist.
    """

    def get_tool_definition(self) -> Tool:
        return Tool(
            name="attach_file_from_url",
            description="Downloads a file from a URL and attaches it to a Jira issue. Perfect for production environments.",
            inputSchema={
                "type": "object",
                "properties": {
                    "issueKey": {
                        "type": "string",
                        "description": "The key of the issue to attach the file to (e.g., 'TEST-34').",
                    },
                    "file_url": {
                        "type": "string",
                        "description": "URL to download the file from (e.g., 'https://example.com/document.pdf').",
                    },
                    "filename": {
                        "type": "string",
                        "description": "Optional custom filename for the attachment. If not provided, will be extracted from URL.",
                    },
                },
                "required": ["issueKey", "file_url"],
            },
        )

    async def execute(
        self, arguments: dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        if not cloudid:
            raise RuntimeError("Cloud ID context is missing.")

        issue_key = arguments.get("issueKey")
        file_url = arguments.get("file_url")
        filename = arguments.get("filename")

        if not issue_key:
            raise ValueError("issueKey is required.")
        if not file_url:
            raise ValueError("file_url is required.")

        # Extract filename from URL if not provided
        if not filename:
            parsed_url = urlparse(file_url)
            filename = parsed_url.path.split('/')[-1] or 'attachment'

        try:
            print(f"DEBUG: Downloading and attaching file '{filename}' from {file_url} to {issue_key}")
            
            attachments = jira_fetcher.post_attachment_from_url(
                issue_key=issue_key,
                file_url=file_url,
                filename=filename
            )

            if isinstance(attachments, list) and attachments:
                attachment = attachments[0]
                result = {
                    "success": True,
                    "message": f"File '{filename}' downloaded from URL and attached successfully.",
                    "attachment": {
                        "id": attachment.get("id"),
                        "filename": attachment.get("filename"),
                        "size_bytes": attachment.get("size"),
                        "content_type": attachment.get("mimeType"),
                        "created": attachment.get("created"),
                        "author": attachment.get("author", {}).get("displayName")
                    },
                    "source_url": file_url
                }
            else:
                result = {
                    "success": True,
                    "message": "Attachment API call succeeded, but no attachment info was returned.",
                    "source_url": file_url
                }

        except Exception as e:
            result = {
                "success": False,
                "error": str(e),
                "source_url": file_url,
                "attempted_filename": filename
            }

        return [TextContent(type="text", text=json.dumps(result, indent=2))]
