import json
from typing import Dict, List, Optional

from mcp.types import Text<PERSON>ontent, Tool

from .base import BaseTool


class AddWorklogTool(BaseTool):
    """
    Adds a worklog to an existing Jira issue.
    """

    def get_tool_definition(self) -> Tool:
        return Tool(
            name="add_worklog",
            description="Adds a worklog to a Jira issue.",
            inputSchema={
                "type": "object",
                "properties": {
                    "issueKey": {
                        "type": "string",
                        "description": "Key of the issue to add the worklog to (e.g., 'TEST-48').",
                    },
                    "timeSpent": {
                        "type": "string",
                        "description": "The amount of time spent, e.g., '1w 2d 3h 4m'.",
                    },
                    "comment": {
                        "type": "string",
                        "description": "A comment to add to the worklog (optional).",
                    },
                },
                "required": ["issueKey", "timeSpent"],
            },
        )

    async def execute(
        self, arguments: Dict, jira_fetcher, cloudid: Optional[str] = None
    ) -> List[TextContent]:
        if not cloudid:
            raise RuntimeError("Missing cloudid")
        issue_key = arguments.get("issueKey")
        if not issue_key:
            raise ValueError("issueKey is required")

        worklog_payload = {
            "timeSpent": arguments["timeSpent"],
        }
        if "comment" in arguments:
            worklog_payload["comment"] = {
                "type": "doc",
                "version": 1,
                "content": [
                    {
                        "type": "paragraph",
                        "content": [{"type": "text", "text": arguments["comment"]}]
                    }
                ]
            }

        worklog_url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/issue/{issue_key}/worklog"
        worklog_response = jira_fetcher.post(worklog_url, json=worklog_payload)

        if not worklog_response.ok:
            raise RuntimeError(
                f"Failed to add worklog: {worklog_response.status_code} - {worklog_response.text}"
            )

        response = {
            "key": issue_key,
            "message": "Successfully added worklog.",
            "url": f"https://id.atlassian.com/browse/{issue_key}",
        }

        return [TextContent(type="text", text=json.dumps(response, indent=2))]


def factory():
    """
    Factory function to create an instance of the AddWorklogTool.
    """
    return AddWorklogTool()
