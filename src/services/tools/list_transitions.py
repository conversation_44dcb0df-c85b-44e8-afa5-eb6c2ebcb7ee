import json
from typing import List

from mcp.types import TextContent, Tool

from .base import BaseTool


class ListTransitionsTool(BaseTool):
    def get_tool_definition(self) -> Tool:
        return Tool(
            name="list_available_transitions",
            description="Lists all possible transition actions for a given Jira issue from its current status.",
            inputSchema={
                "type": "object",
                "properties": {
                    "issueKey": {
                        "type": "string",
                        "description": "The key of the issue to check (e.g., 'TEST-34').",
                    }
                },
                "required": ["issueKey"],
            },
        )

    async def execute(
        self, arguments: dict, jira_fetcher, cloudid
    ) -> List[TextContent]:
        issue_key = arguments["issueKey"]

        url = f"https://api.atlassian.com/ex/jira/{cloudid}/rest/api/3/issue/{issue_key}/transitions"
        headers = {"Accept": "application/json"}

        response = jira_fetcher.get(url, headers=headers)
        if not response.ok:
            return [
                TextContent(
                    type="text",
                    text=json.dumps(
                        {
                            "message": "Failed to list transitions",
                            "status_code": response.status_code,
                            "response": response.text,
                        }
                    ),
                )
            ]

        data = response.json()
        transitions = data.get("transitions", [])
        available_actions = [
            {"id": t.get("id"), "name": t.get("name")} for t in transitions
        ]

        return [TextContent(type="text", text=json.dumps(available_actions))]
