class InMemoryEventStore:
    """Minimal stub for MCP event store compatibility."""

    def __init__(self, max_events_per_stream=100):
        self.max_events_per_stream = max_events_per_stream
        self.store = {}

    async def store_event(self, stream_id, message):
        # Minimal implementation: just store the event in memory
        if stream_id not in self.store:
            self.store[stream_id] = []
        self.store[stream_id].append(message)
        return str(len(self.store[stream_id]))  # Return event_id as string
