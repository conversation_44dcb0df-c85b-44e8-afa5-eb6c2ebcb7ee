"""
JiraClient class for making authenticated API calls to <PERSON>ra.
Uses OAuthConfig for authentication and provides methods for common API operations.
"""

import os
import time
from typing import Any, Dict, List, Optional, Union

import requests
from helper.logger import logger
from helper.oauth_config import OAuthConfig
from utils.cloud_id_utils import get_api_url as utils_get_api_url
from utils.cloud_id_utils import get_cloud_id_from_api


class JiraClient:
    """
    Base class for making authenticated API calls to Jira.
    Uses OAuthConfig for authentication and handles token refresh.
    """

    def __init__(
        self,
        oauth_config: OAuthConfig,
        client_id: Optional[str] = None,
        client_secret: Optional[str] = None,
        cloud_id: Optional[str] = None,
        token_url: str = "https://auth.atlassian.com/oauth/token",
    ):
        """
        Initialize the JiraClient with OAuth configuration.

        Args:
            oauth_config: The OAuth configuration to use for authentication
            client_id: The client ID for token refresh (defaults to env var)
            client_secret: The client secret for token refresh (defaults to env var)
            cloud_id: The Jira Cloud ID (defaults to env var)
            token_url: The URL for token refresh
        """
        self.oauth_config = oauth_config
        self.client_id = client_id or os.environ.get("JIRA_CLIENT_ID")
        self.client_secret = client_secret or os.environ.get("JIRA_CLIENT_SECRET")
        self.token_url = token_url
        self.session = requests.Session()

        # Initialize cloud ID from parameter, OAuth config, or environment variable
        self.cloudid = cloud_id
        if not self.cloudid:
            # Try to get from OAuth config
            config_cloud_id = self.oauth_config.get_cloud_id()
            if config_cloud_id:
                self.cloudid = config_cloud_id

    def _get_headers(self) -> Dict[str, str]:
        """Get headers with authentication token."""
        return {
            "Authorization": f"Bearer {self.oauth_config.get_access_token()}",
            "Accept": "application/json",
            "Content-Type": "application/json",
        }

    def _refresh_access_token(self) -> str:
        """
        Refresh the access token using the refresh token.

        Returns:
            The new access token

        Raises:
            Exception: If token refresh fails
        """
        refresh_token = self.oauth_config.get_refresh_token()
        if not refresh_token:
            raise Exception("No refresh token available")

        payload = {
            "grant_type": "refresh_token",
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "refresh_token": refresh_token,
        }

        resp = requests.post(self.token_url, json=payload)
        if not resp.ok:
            raise Exception(
                f"Failed to refresh access token: {resp.status_code} {resp.text}"
            )

        token_data = resp.json()
        new_access_token = token_data["access_token"]
        new_refresh_token = token_data.get("refresh_token", refresh_token)

        # Update the OAuth config with new tokens
        self.oauth_config.update_tokens(new_access_token, new_refresh_token)

        return new_access_token

    def request(self, method: str, url: str, **kwargs) -> requests.Response:
        """
        Make an authenticated request to the Jira API.

        Args:
            method: HTTP method (GET, POST, PUT, DELETE)
            url: API endpoint URL
            **kwargs: Additional arguments to pass to requests

        Returns:
            Response object

        Raises:
            Exception: If the request fails after token refresh
        """
        # Add Authorization header
        headers = kwargs.pop("headers", {}) or {}
        headers.update(self._get_headers())
        kwargs["headers"] = headers

        # Log the request details for debugging
        logger.debug(f"Making {method} request to {url}")
        if method in ("POST", "PUT") and "json" in kwargs:
            logger.debug(f"Request payload: {kwargs['json']}")

        resp = self.session.request(method, url, **kwargs)

        # Log the response status and content for debugging
        logger.debug(f"Response status: {resp.status_code}")
        if not resp.ok:
            try:
                error_content = resp.json()
                logger.error(f"Error response: {error_content}")
            except:
                logger.error(f"Error response text: {resp.text}")

        # If unauthorized, try to refresh token and retry once
        if resp.status_code in (401, 403):
            try:
                logger.info("Attempting to refresh access token")
                self._refresh_access_token()
                headers.update(self._get_headers())
                kwargs["headers"] = headers
                logger.info("Retrying request with new access token")
                resp = self.session.request(method, url, **kwargs)

                # Log the retry response status
                logger.debug(f"Retry response status: {resp.status_code}")
                if not resp.ok:
                    try:
                        error_content = resp.json()
                        logger.error(f"Retry error response: {error_content}")
                    except:
                        logger.error(f"Retry error response text: {resp.text}")
            except Exception as e:
                logger.error(f"Token refresh failed: {e}")
                # Re-raise the original response error
                resp.raise_for_status()

        return resp

    def get(self, url: str, **kwargs) -> requests.Response:
        """Make a GET request to the Jira API."""
        return self.request("GET", url, **kwargs)

    def post(self, url: str, **kwargs) -> requests.Response:
        """Make a POST request to the Jira API."""
        return self.request("POST", url, **kwargs)

    def put(self, url: str, **kwargs) -> requests.Response:
        """Make a PUT request to the Jira API."""
        return self.request("PUT", url, **kwargs)

    def delete(self, url: str, **kwargs) -> requests.Response:
        """Make a DELETE request to the Jira API."""
        return self.request("DELETE", url, **kwargs)

    def get_cloud_id(self) -> Optional[str]:
        """
        Get the Jira Cloud ID for the authenticated user.

        Returns:
            The cloud ID or None if not found
        """
        import logging

        # First check if we already have a cloud ID
        if self.cloudid:
            logging.debug(f"Using cached cloud ID: {self.cloudid}")
            return self.cloudid

        # Then check if the OAuth config has a cloud ID
        config_cloud_id = self.oauth_config.get_cloud_id()
        if config_cloud_id:
            logging.debug(f"Using cloud ID from OAuth config: {config_cloud_id}")
            self.cloudid = config_cloud_id
            return self.cloudid

        # If not, fetch it from the API using our utility function
        logging.info("No cloud ID found in cache or OAuth config, fetching from API")
        try:
            # Get the access token from OAuth config
            access_token = self.oauth_config.get_access_token()
            if not access_token:
                logging.error("No access token available to fetch cloud ID")
                return None

            # Use the utility function to get the cloud ID
            self.cloudid = get_cloud_id_from_api(access_token)

            if self.cloudid:
                # Update the OAuth config with the cloud ID
                self.oauth_config.update_cloud_id(self.cloudid)
                return self.cloudid
        except Exception as e:
            import traceback

            logging.error(f"Failed to get cloud ID: {e}")
            logging.error(f"Traceback: {traceback.format_exc()}")

        logging.warning(
            "Could not determine cloud ID. This will cause API calls to fail."
        )
        return None

    def get_api_url(self, path: str) -> str:
        """
        Get the full API URL for a given path.

        Args:
            path: The API path (e.g., "/rest/api/3/issue")

        Returns:
            The full API URL

        Raises:
            ValueError: If cloud ID is not available
        """
        import logging

        cloud_id = self.get_cloud_id()
        if not cloud_id:
            logging.error(
                "Cloud ID not available. This is required for Jira Cloud API calls."
            )
            raise ValueError(
                "Cloud ID not available. Unable to retrieve cloud ID from Atlassian API."
            )

        # Use the utility function to construct the API URL
        return utils_get_api_url(cloud_id, path)
