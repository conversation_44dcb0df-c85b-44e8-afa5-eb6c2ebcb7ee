JIRA_SCOPES = [
    # --- Jira Platform API Scopes ---
    "read:jira-user",
    "read:jira-work",
    "write:jira-work",
    "manage:jira-project",
    "manage:jira-configuration",
    # --- Jira Software API Scopes (Full Management) ---
    "read:board-scope:jira-software",
    "write:board-scope:jira-software",
    "read:sprint:jira-software",
    "write:sprint:jira-software",
    "delete:sprint:jira-software",
    "manage:sprint:jira-software",
    "offline_access",
]

CONFLUENCE_SCOPES = [
    "read:page:confluence",
    "read:space:confluence",
    "write:page:confluence",
    "read:content.all:confluence",
    "read:content.permission:confluence",
    "read:space.content:confluence",
    "write:content:confluence",
    "write:content.permission:confluence",
    "write:space.content:confluence",
    "search:confluence",
    "read:comment:confluence",
    "delete:page:confluence",
]

ALL_SCOPES = JIRA_SCOPES + CONFLUENCE_SCOPES
