# import os

# from helper.config import (
#     JIRA_CLIENT_ID,
#     JIRA_CLIENT_SECRET,
#     JIRA_REDIRECT_URI,
#     JIRA_URL,
# )
# from requests_oauthlib import OAuth2Session

# # OAuth2 endpoints
# authorization_base_url = "https://auth.atlassian.com/authorize"
# token_url = "https://auth.atlassian.com/oauth/token"
# # Note: The token URL is the same for all Atlassian cloud products.

# # Scopes for Jira API access
# scopes = [
#     "read:jira-user",
#     "read:jira-work",
#     "write:jira-work",
#     "manage:jira-project",
#     "manage:jira-configuration",
#     "offline_access",
# ]


# def get_jira_oauth_session(token=None, state=None, token_updater=None):
#     """
#     Creates and returns an OAuth2Session object for Jira.
#     """
#     session = OAuth2Session(
#         client_id=JIRA_CLIENT_ID,
#         redirect_uri=JIRA_REDIRECT_URI,
#         scope=scopes,
#         token=token,
#         state=state,
#         auto_refresh_url=token_url,
#         token_updater=token_updater,
#     )
#     return session


# def get_authorization_url():
#     """
#     Generates the Jira authorization URL.
#     """
#     jira_session = get_jira_oauth_session()
#     authorization_url, state = jira_session.authorization_url(
#         authorization_base_url, audience="api.atlassian.com"
#     )
#     return authorization_url, state


# def fetch_token(code, state):
#     print("****fetch_token***, state: ", state, " code: ", code)
#     """
#     Fetches the access token from Jira using the authorization code.
#     """
#     jira_session = get_jira_oauth_session(state=state)
#     token = jira_session.fetch_token(
#         token_url, client_secret=JIRA_CLIENT_SECRET, code=code
#     )
#     return token


import json
import os

from helper.config import (
    JIRA_CLIENT_ID,
    JIRA_CLIENT_SECRET,
    JIRA_REDIRECT_URI,
    JIRA_URL,
)
from helper.scopes import ALL_SCOPES
from requests_oauthlib import OAuth2Session

# OAuth2 endpoints
authorization_base_url = "https://auth.atlassian.com/authorize"
token_url = "https://auth.atlassian.com/oauth/token"
TOKEN_FILE = "jira_token.json"

# Scopes for Jira API access
scopes = ALL_SCOPES


def save_token(token):
    with open(TOKEN_FILE, "w") as f:
        json.dump(token, f)


def load_token():
    if os.path.exists(TOKEN_FILE):
        with open(TOKEN_FILE, "r") as f:
            return json.load(f)
    return None


# scopes = ["read:jira-work", "write:jira-work", "offline_access"]


def get_jira_oauth_session(token=None, state=None, token_updater=None):
    if token is not None and "access_token" not in token:
        raise ValueError("Invalid or missing access_token in provided token.")

    session = OAuth2Session(
        client_id=JIRA_CLIENT_ID,
        redirect_uri=JIRA_REDIRECT_URI,
        scope=scopes,
        token=token,
        state=state,
        auto_refresh_url=token_url,
        token_updater=token_updater,
    )

    def protected_request_hook(url, headers, data):
        if headers is None:
            headers = {}
        if token and "access_token" in token:
            headers["Authorization"] = f'Bearer {token["access_token"]}'
        print(f"Setting Authorization header: {headers.get('Authorization')}")
        return url, headers, data

    session.register_compliance_hook("protected_request", protected_request_hook)

    return session


def get_authorization_url():
    jira_session = get_jira_oauth_session()
    authorization_url, state = jira_session.authorization_url(
        authorization_base_url, audience="api.atlassian.com"
    )
    return authorization_url, state


def fetch_token(code, state):
    print("****fetch_token***, state: ", state, " code: ", code)
    jira_session = get_jira_oauth_session(state=state)
    # Request expires_in=86400 (1 day), but Atlassian may ignore this and use their default expiry.
    # The refresh token can always be used to get a new access token.
    token = jira_session.fetch_token(
        token_url,
        client_secret=JIRA_CLIENT_SECRET,
        code=code,
        include_client_id=True,
        expires_in=86400,  # 1 day in seconds
    )
    print("Fetched token:", token)
    return token


# --- Refresh Token Handling Example ---

import os
import time

import requests


class JiraOAuthSession:
    """
    Wraps a requests.Session and transparently refreshes the access token using the refresh token when needed.
    Usage:
        session = JiraOAuthSession(
            access_token=...,
            refresh_token=...,
            client_id=JIRA_CLIENT_ID,
            client_secret=JIRA_CLIENT_SECRET,
            token_updater=save_tokens_callback  # <-- pass a function to persist tokens
        )
        response = session.request("GET", "https://api.atlassian.com/ex/jira/{cloudid}/rest/api/2/...")
    """

    def __init__(
        self,
        access_token,
        refresh_token,
        client_id,
        client_secret,
        token_url="https://auth.atlassian.com/oauth/token",
        token_updater=None,  # <-- callback to persist tokens
    ):
        self.access_token = access_token
        self.refresh_token = refresh_token
        self.client_id = client_id
        self.client_secret = client_secret
        self.token_url = token_url
        self.session = requests.Session()
        self.token_expiry = None  # Optionally track expiry
        self.token_updater = token_updater

    def _refresh_access_token(self):
        payload = {
            "grant_type": "refresh_token",
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "refresh_token": self.refresh_token,
        }
        resp = requests.post(self.token_url, json=payload)
        if not resp.ok:
            raise Exception(
                f"Failed to refresh access token: {resp.status_code} {resp.text}"
            )
        token_data = resp.json()
        self.access_token = token_data["access_token"]
        self.refresh_token = token_data.get("refresh_token", self.refresh_token)
        self.token_expiry = time.time() + token_data.get("expires_in", 3600)
        # Persist new tokens if callback provided
        if self.token_updater:
            self.token_updater(token_data)
        return self.access_token

    def request(self, method, url, **kwargs):
        # Add Authorization header
        headers = kwargs.pop("headers", {}) or {}
        headers["Authorization"] = f"Bearer {self.access_token}"
        kwargs["headers"] = headers

        resp = self.session.request(method, url, **kwargs)
        if resp.status_code in (401, 403):
            # Try to refresh token and retry once
            try:
                self._refresh_access_token()
                headers["Authorization"] = f"Bearer {self.access_token}"
                kwargs["headers"] = headers
                resp = self.session.request(method, url, **kwargs)
            except Exception as e:
                print(f"Token refresh failed: {e}")
                raise
        return resp

    def post_attachment(
        self,
        url: str,
        filepath: str = None,
        filename: str = None,
        file_content: bytes = None,
        content_type: str = None,
    ):
        """
        Handles the special case of uploading a file as multipart/form-data.
        This method supports multiple attachment methods for both local and production environments.

        Args:
            url: The Jira API endpoint URL for attachments
            filepath: Local file path (for local development)
            filename: Name for the attachment
            file_content: Raw file content as bytes (for production/remote environments)
            content_type: MIME type of the file (optional, defaults to application/octet-stream)

        Note: Either filepath OR (file_content + filename) must be provided.
        """
        # Validate input parameters
        if not filepath and not (file_content and filename):
            raise ValueError(
                "Either filepath or (file_content + filename) must be provided"
            )

        if filepath and file_content:
            raise ValueError("Cannot specify both filepath and file_content")

        # First, check if the token might need refreshing before the attempt.
        if self.token_expiry and self.token_expiry < time.time() + 10:
            try:
                self._refresh_access_token()
            except Exception as e:
                print(f"Preemptive token refresh failed: {e}")

        # Prepare the specific headers needed for the attachment upload
        upload_headers = {
            "X-Atlassian-Token": "no-check",  # Essential for file uploads
            "Authorization": f"Bearer {self.access_token}",
        }

        # Determine content type
        if not content_type:
            content_type = "application/octet-stream"

        # Handle different attachment methods
        if filepath:
            # Local file path method (for development)
            if not filename:
                filename = os.path.basename(filepath)

            with open(filepath, "rb") as f:
                files_payload = {"file": (filename, f, content_type)}
                response = self.session.post(
                    url, headers=upload_headers, files=files_payload
                )
        else:
            # File content method (for production/remote environments)
            import io

            file_obj = io.BytesIO(file_content)
            files_payload = {"file": (filename, file_obj, content_type)}
            response = self.session.post(
                url, headers=upload_headers, files=files_payload
            )

        # Handle 401/403 errors by refreshing the token and retrying ONCE
        if response.status_code in (401, 403):
            print(
                "INFO: Attachment upload failed with 401/403, attempting token refresh and retry..."
            )
            try:
                self._refresh_access_token()
                upload_headers["Authorization"] = f"Bearer {self.access_token}"

                # Retry the upload with refreshed token
                if filepath:
                    with open(filepath, "rb") as f_retry:
                        files_payload_retry = {
                            "file": (filename, f_retry, content_type)
                        }
                        response = self.session.post(
                            url, headers=upload_headers, files=files_payload_retry
                        )
                else:
                    file_obj_retry = io.BytesIO(file_content)
                    files_payload_retry = {
                        "file": (filename, file_obj_retry, content_type)
                    }
                    response = self.session.post(
                        url, headers=upload_headers, files=files_payload_retry
                    )

            except Exception as e:
                print(f"Token refresh failed during attachment upload: {e}")
                raise

        return response

    def get(self, url, **kwargs):
        return self.request("GET", url, **kwargs)

    def post(self, url, **kwargs):
        return self.request("POST", url, **kwargs)

    def put(self, url, **kwargs):
        return self.request("PUT", url, **kwargs)

    def delete(self, url, **kwargs):
        return self.request("DELETE", url, **kwargs)
