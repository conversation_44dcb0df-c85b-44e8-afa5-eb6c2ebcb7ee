"""
OAuth configuration classes for Jira MCP.
Provides different OAuth configuration strategies for authentication.
"""

from abc import ABC, abstractmethod
from typing import Dict, Optional


class OAuthConfig(ABC):
    """
    Abstract base class for OAuth configuration.
    Different implementations can provide different ways to obtain tokens.
    """

    @abstractmethod
    def get_access_token(self) -> str:
        """Get the access token for API calls."""
        pass

    @abstractmethod
    def get_refresh_token(self) -> Optional[str]:
        """Get the refresh token for refreshing access tokens."""
        pass

    def get_cloud_id(self) -> Optional[str]:
        """Get the cloud ID for Jira API calls."""
        return None

    @abstractmethod
    def update_tokens(
        self, access_token: str, refresh_token: Optional[str] = None
    ) -> None:
        """Update tokens after a refresh."""
        pass

    def update_cloud_id(self, cloud_id: str) -> None:
        """Update cloud ID."""
        pass


class BYOAccessTokenOAuthConfig(OAuthConfig):
    """
    'Bring Your Own Access Token' OAuth configuration.
    Uses tokens provided directly by the client.
    """

    def __init__(
        self,
        access_token: str,
        refresh_token: Optional[str] = None,
        cloud_id: Optional[str] = None,
    ):
        """
        Initialize with provided tokens.

        Args:
            access_token: The access token for API calls
            refresh_token: Optional refresh token for refreshing the access token
            cloud_id: Optional cloud ID for Jira API calls
        """
        self._access_token = access_token
        self._refresh_token = refresh_token
        self._cloud_id = cloud_id

    def get_access_token(self) -> str:
        """Get the access token for API calls."""
        return self._access_token

    def get_refresh_token(self) -> Optional[str]:
        """Get the refresh token for refreshing access tokens."""
        return self._refresh_token

    def get_cloud_id(self) -> Optional[str]:
        """Get the cloud ID for Jira API calls."""
        return self._cloud_id

    def update_tokens(
        self, access_token: str, refresh_token: Optional[str] = None
    ) -> None:
        """Update tokens after a refresh."""
        self._access_token = access_token
        if refresh_token:
            self._refresh_token = refresh_token

    def update_cloud_id(self, cloud_id: str) -> None:
        """Update cloud ID."""
        self._cloud_id = cloud_id


class AuthContextOAuthConfig(OAuthConfig):
    """
    OAuth configuration that uses the current auth context.
    """

    def __init__(self):
        """Initialize with the current auth context."""
        from context.auth_context import get_current_auth_context

        self._auth_context = get_current_auth_context()

    def get_access_token(self) -> str:
        """Get the access token from the auth context."""
        if not self._auth_context or not self._auth_context.access_token:
            raise ValueError("No access token available in auth context")
        return self._auth_context.access_token

    def get_refresh_token(self) -> Optional[str]:
        """Get the refresh token from the auth context."""
        if not self._auth_context:
            return None
        return self._auth_context.refresh_token

    def get_cloud_id(self) -> Optional[str]:
        """Get the cloud ID from the auth context."""
        if not self._auth_context:
            return None
        return getattr(self._auth_context, "cloud_id", None)

    def update_tokens(
        self, access_token: str, refresh_token: Optional[str] = None
    ) -> None:
        """Update tokens in the auth context."""
        if not self._auth_context:
            from context.auth_context import AuthContext, set_current_auth_context

            self._auth_context = AuthContext(
                access_token=access_token, refresh_token=refresh_token
            )
            set_current_auth_context(self._auth_context)
        else:
            self._auth_context.access_token = access_token
            if refresh_token:
                self._auth_context.refresh_token = refresh_token

    def update_cloud_id(self, cloud_id: str) -> None:
        """Update cloud ID in the auth context."""
        if not self._auth_context:
            from context.auth_context import AuthContext, set_current_auth_context

            self._auth_context = AuthContext(cloud_id=cloud_id)
            set_current_auth_context(self._auth_context)
        else:
            self._auth_context.cloud_id = cloud_id
