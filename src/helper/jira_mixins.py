"""
Mixins for Jira operations.
Each mixin provides a specific set of Jira API operations.
"""

import json
from typing import Any, Dict, List, Optional, Union


# This helper function is crucial for v3 and can be shared across mixins
def _create_adf_from_string(text_content: str) -> Optional[Dict]:
    """Creates a valid Atlassian Document Format (ADF) JSON object from plain text."""
    if not text_content:
        return None
    # Create paragraphs for each line of text
    paragraphs = [
        {"type": "paragraph", "content": [{"type": "text", "text": p}]}
        for p in text_content.strip().split("\n")
        if p
    ]
    if not paragraphs:
        return None
    return {"type": "doc", "version": 1, "content": paragraphs}


class JiraIssueMixin:
    """Mixin for Jira v3 issue operations."""

    def get_issue(self, issue_key: str, fields: List[str] = None) -> Dict[str, Any]:
        """Gets a Jira issue by key using the v3 API."""
        url = self.get_api_url(f"rest/api/3/issue/{issue_key}")
        params = {}
        if fields:
            params["fields"] = ",".join(fields)

        response = self.get(url, params=params)
        response.raise_for_status()
        return response.json()

    def create_issue(self, fields: Dict[str, Any]) -> Dict[str, Any]:
        """
        Creates a Jira issue using the v3 API. The 'description' field in the
        'fields' dict must be converted to ADF format beforehand.
        """
        url = self.get_api_url("rest/api/3/issue")
        payload = {"fields": fields}

        response = self.post(url, json=payload)
        response.raise_for_status()
        return response.json()

    def update_issue_fields(self, issue_key: str, fields: Dict[str, Any]):
        """Updates fields on a Jira issue using the v3 API."""
        url = self.get_api_url(f"rest/api/3/issue/{issue_key}")
        payload = {"fields": fields}

        response = self.put(url, json=payload)
        response.raise_for_status()
        # A successful PUT returns 204 No Content, so there is no JSON body.

    def delete_issue(self, issue_key: str):
        """Deletes a Jira issue using the v3 API."""
        url = self.get_api_url(f"rest/api/3/issue/{issue_key}")
        response = self.delete(url)
        response.raise_for_status()
        # A successful DELETE returns 204 No Content.

    def search_issues(
        self, jql: str, max_results: int = 50, fields: List[str] = None
    ) -> List[Dict[str, Any]]:
        """Searches for Jira issues using JQL with the v3 API."""
        url = self.get_api_url("rest/api/3/search")
        payload = {"jql": jql, "maxResults": max_results}
        if fields:
            payload["fields"] = fields

        response = self.post(url, json=payload)
        response.raise_for_status()
        return response.json().get("issues", [])


class JiraCommentMixin:
    """Mixin for Jira v3 comment operations."""

    def add_comment(self, issue_key: str, comment_text: str) -> Dict[str, Any]:
        """Adds a comment to a Jira issue, converting text to ADF for v3."""
        url = self.get_api_url(f"rest/api/3/issue/{issue_key}/comment")
        adf_body = _create_adf_from_string(comment_text)
        if not adf_body:
            raise ValueError("Comment text cannot be empty.")

        payload = {"body": adf_body}
        response = self.post(url, json=payload)
        response.raise_for_status()
        return response.json()

    def get_comments(self, issue_key: str) -> List[Dict[str, Any]]:
        """Gets comments for a Jira issue using the v3 API."""
        url = self.get_api_url(f"rest/api/3/issue/{issue_key}/comment")
        response = self.get(url)
        response.raise_for_status()
        return response.json().get("comments", [])


class JiraAttachmentMixin:
    """Mixin for Jira v3 attachment operations."""

    def post_attachment(
        self,
        issue_key: str,
        filepath: str = None,
        filename: str = None,
        file_content: bytes = None,
        content_type: str = None,
    ):
        """
        Attaches a file to a Jira issue using multiple supported methods.

        Args:
            issue_key: The Jira issue key (e.g., 'TEST-123')
            filepath: Local file path (for development environments)
            filename: Name for the attachment
            file_content: Raw file content as bytes (for production environments)
            content_type: MIME type of the file

        Note: Either filepath OR (file_content + filename) must be provided.
        """
        url = self.get_api_url(f"rest/api/3/issue/{issue_key}/attachments")

        response = self.session.post_attachment(
            url=url,
            filepath=filepath,
            filename=filename,
            file_content=file_content,
            content_type=content_type,
        )

        response.raise_for_status()
        return response.json()

    def post_attachment_from_url(
        self, issue_key: str, file_url: str, filename: str = None
    ):
        """
        Downloads a file from a URL and attaches it to a Jira issue.
        Useful for production environments where you need to attach remote files.

        Args:
            issue_key: The Jira issue key (e.g., 'TEST-123')
            file_url: URL to download the file from
            filename: Optional filename (defaults to URL basename)
        """
        from urllib.parse import urlparse

        import requests

        if not filename:
            parsed_url = urlparse(file_url)
            filename = parsed_url.path.split("/")[-1] or "attachment"

        # Download the file content
        response = requests.get(file_url)
        response.raise_for_status()

        # Determine content type from response headers
        content_type = response.headers.get("content-type", "application/octet-stream")

        # Use the content-based attachment method
        return self.post_attachment(
            issue_key=issue_key,
            file_content=response.content,
            filename=filename,
            content_type=content_type,
        )


class JiraTransitionMixin:
    """Mixin for Jira v3 transition operations."""

    def list_transitions(self, issue_key: str) -> List[Dict[str, Any]]:
        """Lists available transitions for a Jira issue."""
        url = self.get_api_url(f"rest/api/3/issue/{issue_key}/transitions")
        response = self.get(url)
        response.raise_for_status()
        return response.json().get("transitions", [])

    def transition_issue(self, issue_key: str, transition_id: str):
        """Transitions a Jira issue to a new status."""
        url = self.get_api_url(f"rest/api/3/issue/{issue_key}/transitions")
        payload = {"transition": {"id": transition_id}}

        response = self.post(url, json=payload)
        response.raise_for_status()
        # A successful transition returns 204 No Content.


class JiraProjectMixin:
    """Mixin for Jira v3 project operations."""

    def list_projects(self) -> List[Dict[str, Any]]:
        """Lists all Jira projects accessible to the user."""
        # The v2 endpoint is still commonly used and stable for this.
        # However, a paginated v3 endpoint exists at /rest/api/3/project/search
        url = self.get_api_url("rest/api/3/project")
        response = self.get(url)
        response.raise_for_status()
        return response.json()


class JiraUserMixin:
    """Mixin for Jira v3 user operations."""

    def find_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """Finds a single Jira user by their exact email address (v3)."""
        if not email:
            return None
        url = self.get_api_url("rest/api/3/user/search")
        params = {"query": email}

        response = self.get(url, params=params)
        response.raise_for_status()
        users = response.json()

        # Find the user with the exact, case-insensitive email match
        for user in users:
            if user.get("emailAddress", "").lower() == email.lower():
                return user
        return None

    def get_my_permissions(self, project_key: str = None) -> Dict[str, Any]:
        """Gets permissions for the current user (v3)."""
        url = self.get_api_url("rest/api/3/mypermissions")
        payload = {
            "permissions": "EDIT_ISSUES,CREATE_ISSUES,ADD_COMMENTS,CREATE_ATTACHMENTS,DELETE_ISSUES,LINK_ISSUES,TRANSITION_ISSUES"
        }
        if project_key:
            payload["projectKey"] = project_key

        response = self.post(url, json=payload)  # mypermissions is a POST in v3
        response.raise_for_status()
        return response.json().get("permissions", {})


# class JiraCommentMixin:
#     """Mixin for Jira comment operations."""

#     def add_comment(self, issue_key: str, comment: str) -> Dict[str, Any]:
#         """
#         Add a comment to a Jira issue.

#         Args:
#             issue_key: The Jira issue key (e.g., 'TEST-1')
#             comment: The comment text

#         Returns:
#             Dict with comment ID and success status
#         """
#         url = self.get_api_url(f"rest/api/2/issue/{issue_key}/comment")

#         response = self.post(url, json={"body": comment})
#         response.raise_for_status()
#         data = response.json()

#         return {
#             "id": data.get("id"),
#             "success": True,
#             "issue_key": issue_key,
#         }

#     def get_comments(self, issue_key: str) -> List[Dict[str, Any]]:
#         """
#         Get comments for a Jira issue.

#         Args:
#             issue_key: The Jira issue key (e.g., 'TEST-1')

#         Returns:
#             List of comments
#         """
#         url = self.get_api_url(f"rest/api/2/issue/{issue_key}/comment")

#         response = self.get(url)
#         response.raise_for_status()
#         data = response.json()

#         return [
#             {
#                 "id": comment.get("id"),
#                 "body": comment.get("body"),
#                 "author": comment.get("author", {}).get("displayName"),
#                 "created": comment.get("created"),
#             }
#             for comment in data.get("comments", [])
#         ]


# class JiraAttachmentMixin:
#     """Mixin for Jira attachment operations."""

#     def attach_file(
#         self, issue_key: str, file_path: str, filename: Optional[str] = None
#     ) -> Dict[str, Any]:
#         """
#         Attach a file to a Jira issue.

#         Args:
#             issue_key: The Jira issue key (e.g., 'TEST-1')
#             file_path: Path to the file to attach
#             filename: Optional filename to use (defaults to the file's basename)

#         Returns:
#             Dict with attachment ID and success status
#         """
#         import os

#         url = self.get_api_url(f"rest/api/2/issue/{issue_key}/attachments")

#         headers = self._get_headers()
#         # Remove Content-Type as it will be set by requests for multipart/form-data
#         if "Content-Type" in headers:
#             del headers["Content-Type"]

#         # Add the special header required for attachments
#         headers["X-Atlassian-Token"] = "no-check"

#         filename = filename or os.path.basename(file_path)

#         with open(file_path, "rb") as f:
#             files = {"file": (filename, f)}
#             response = self.session.post(url, headers=headers, files=files)

#         response.raise_for_status()
#         data = response.json()

#         return {
#             "id": data[0].get("id") if data else None,
#             "success": True,
#             "issue_key": issue_key,
#             "filename": filename,
#         }

#     def attach_content(
#         self, issue_key: str, content: str, filename: str
#     ) -> Dict[str, Any]:
#         """
#         Attach content as a file to a Jira issue.

#         Args:
#             issue_key: The Jira issue key (e.g., 'TEST-1')
#             content: Content to attach
#             filename: Filename to use for the attachment

#         Returns:
#             Dict with attachment ID and success status
#         """
#         import io

#         url = self.get_api_url(f"rest/api/2/issue/{issue_key}/attachments")

#         headers = self._get_headers()
#         # Remove Content-Type as it will be set by requests for multipart/form-data
#         if "Content-Type" in headers:
#             del headers["Content-Type"]

#         # Add the special header required for attachments
#         headers["X-Atlassian-Token"] = "no-check"

#         content_bytes = content.encode("utf-8") if isinstance(content, str) else content
#         files = {"file": (filename, io.BytesIO(content_bytes))}

#         response = self.session.post(url, headers=headers, files=files)
#         response.raise_for_status()
#         data = response.json()

#         return {
#             "id": data[0].get("id") if data else None,
#             "success": True,
#             "issue_key": issue_key,
#             "filename": filename,
#         }


# class JiraTransitionMixin:
#     """Mixin for Jira transition operations."""

#     def list_transitions(self, issue_key: str) -> List[Dict[str, Any]]:
#         """
#         List available transitions for a Jira issue.

#         Args:
#             issue_key: The Jira issue key (e.g., 'TEST-1')

#         Returns:
#             List of available transitions
#         """
#         url = self.get_api_url(f"rest/api/2/issue/{issue_key}/transitions")

#         response = self.get(url, params={"expand": "transitions.fields"})
#         response.raise_for_status()
#         data = response.json()

#         return [
#             {
#                 "id": transition["id"],
#                 "name": transition["name"],
#                 "to_status": transition["to"]["name"],
#             }
#             for transition in data.get("transitions", [])
#         ]

#     def transition_issue(
#         self, issue_key: str, transition_id: str, comment: Optional[str] = None
#     ) -> Dict[str, Any]:
#         """
#         Transition a Jira issue to a new status.

#         Args:
#             issue_key: The Jira issue key (e.g., 'TEST-1')
#             transition_id: The ID of the transition to perform
#             comment: Optional comment to add with the transition

#         Returns:
#             Dict with success status
#         """
#         url = self.get_api_url(f"rest/api/2/issue/{issue_key}/transitions")

#         body = {"transition": {"id": transition_id}}

#         if comment:
#             body["update"] = {"comment": [{"add": {"body": comment}}]}

#         response = self.post(url, json=body)
#         response.raise_for_status()

#         return {
#             "success": True,
#             "issue_key": issue_key,
#             "transition_id": transition_id,
#         }


# class JiraProjectMixin:
#     """Mixin for Jira project operations."""

#     def list_projects(self) -> List[Dict[str, Any]]:
#         """
#         List all Jira projects accessible to the user.

#         Returns:
#             List of projects
#         """
#         url = self.get_api_url("rest/api/2/project")

#         response = self.get(url)
#         response.raise_for_status()
#         projects = response.json()

#         return [
#             {
#                 "key": project["key"],
#                 "name": project["name"],
#                 "id": project["id"],
#             }
#             for project in projects
#         ]


# class JiraUserMixin:
#     """Mixin for Jira user operations."""

#     def get_user(self, username: str) -> Dict[str, Any]:
#         """
#         Get a Jira user by username or email.

#         Args:
#             username: The username or email address

#         Returns:
#             Dict with user details
#         """
#         url = self.get_api_url("rest/api/2/user")

#         response = self.get(url, params={"username": username})
#         response.raise_for_status()
#         user = response.json()

#         return {
#             "accountId": user.get("accountId"),
#             "displayName": user.get("displayName"),
#             "emailAddress": user.get("emailAddress"),
#             "active": user.get("active"),
#         }

#     def get_my_permissions(self) -> Dict[str, Any]:
#         """
#         Get permissions for the current user.

#         Returns:
#             Dict with permission details
#         """
#         url = self.get_api_url("rest/api/2/mypermissions")

#         response = self.get(url)
#         response.raise_for_status()
#         data = response.json()

#         return {
#             permission: details.get("havePermission", False)
#             for permission, details in data.get("permissions", {}).items()
#         }
