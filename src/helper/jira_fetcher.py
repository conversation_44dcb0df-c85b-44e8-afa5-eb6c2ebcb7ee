"""
JiraFetcher class that combines all Jira mixins.
Provides a unified interface for all Jira operations.
"""

from typing import Any, Dict, List, Optional, Union

from helper.jira_client import JiraClient
from helper.jira_mixins import (
    JiraAttachmentMixin,
    JiraCommentMixin,
    JiraIssueMixin,
    JiraProjectMixin,
    JiraTransitionMixin,
    JiraUserMixin,
)
from helper.oauth_config import OAuthConfig


class JiraFetcher(
    JiraClient,
    JiraIssueMixin,
    JiraCommentMixin,
    JiraAttachmentMixin,
    JiraTransitionMixin,
    JiraProjectMixin,
    JiraUserMixin,
):
    """
    JiraFetcher class that combines all Jira mixins.
    Provides a unified interface for all Jira operations.
    """

    def __init__(
        self,
        oauth_config: OAuthConfig,
        client_id: Optional[str] = None,
        client_secret: Optional[str] = None,
        cloud_id: Optional[str] = None,
    ):
        """
        Initialize the JiraFetcher with OAuth configuration.

        Args:
            oauth_config: The OAuth configuration to use for authentication
            client_id: The client ID for token refresh (defaults to env var)
            client_secret: The client secret for token refresh (defaults to env var)
            cloud_id: The Jira Cloud ID (defaults to env var)
        """
        super().__init__(oauth_config, client_id, client_secret, cloud_id)


def create_jira_fetcher_from_auth_context(jira_session=None) -> JiraFetcher:
    """
    Create a JiraFetcher instance from the current auth context.

    Args:
        jira_session: Optional pre-configured session to use for API calls

    Returns:
        JiraFetcher instance
    """
    from helper.oauth_config import AuthContextOAuthConfig

    oauth_config = AuthContextOAuthConfig()
    jira_fetcher = JiraFetcher(oauth_config)

    # If a session is provided, use it for API calls
    if jira_session:
        jira_fetcher.session = jira_session

    return jira_fetcher


def create_jira_fetcher_from_tokens(
    access_token: str,
    refresh_token: Optional[str] = None,
    cloud_id: Optional[str] = None,
    client_id: Optional[str] = None,
    client_secret: Optional[str] = None,
) -> JiraFetcher:
    """
    Create a JiraFetcher instance from tokens.

    Args:
        access_token: The access token for API calls
        refresh_token: Optional refresh token for refreshing the access token
        cloud_id: Optional cloud ID for Jira API calls (defaults to env var)
        client_id: The client ID for token refresh (defaults to env var)
        client_secret: The client secret for token refresh (defaults to env var)

    Returns:
        JiraFetcher instance
    """
    from helper.oauth_config import BYOAccessTokenOAuthConfig

    oauth_config = BYOAccessTokenOAuthConfig(access_token, refresh_token, cloud_id)
    return JiraFetcher(oauth_config, client_id, client_secret, cloud_id)
