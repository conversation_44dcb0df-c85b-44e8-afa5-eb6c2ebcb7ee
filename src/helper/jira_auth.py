# from helper.config import JIR<PERSON>_URL
# from helper.jira_oauth import get_jira_oauth_session


# def get_jira_client(access_token: str, refresh_token: str):
#     if not access_token or not refresh_token:
#         raise Exception("Missing access or refresh token.")

#     token = {
#         "access_token": access_token,
#         "refresh_token": refresh_token,
#         "token_type": "Bearer",
#     }

#     jira_session = get_jira_oauth_session(token=token, token_updater=lambda t: None)
#     if jira_session is None:
#         raise Exception("Failed to create OAuth session.")

#     # Ensure Authorization header is set
#     jira_session.headers.update({"Authorization": f"Bearer {access_token}"})
#     print("Initial Session headers:", jira_session.headers)

#     # Test a request to verify headers
#     test_response = jira_session.get(
#         f"{JIRA_URL}/rest/api/3/serverInfo", headers=jira_session.headers
#     )
#     print("Test response headers sent:", jira_session.headers)
#     print("Test response status:", test_response.status_code)
#     print("Test response text:", test_response.text)

#     print("Created session with token:", token.get("access_token")[:10], "...")
#     return jira_session


# AFTER
from helper.config import JIRA_URL
from helper.jira_oauth import get_jira_oauth_session

# from atlassian import Jira  # 1. IMPORT the high-level Jira client
from jira import JIRA


def get_jira_client(access_token: str, refresh_token: str):
    """
    Creates and returns a fully authenticated, high-level Jira API client.
    """
    if not access_token or not refresh_token:
        raise Exception("Missing access or refresh token.")

    token = {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "Bearer",
    }

    # First, get the underlying OAuth session object
    jira_session = get_jira_oauth_session(token=token, token_updater=lambda t: None)
    if jira_session is None:
        raise Exception("Failed to create OAuth session.")

    # 2. CREATE a full Jira client instance using the authenticated session
    # jira = Jira(url=JIRA_URL, session=jira_session)

    jira = JIRA(server=JIRA_URL, token_auth=access_token)
    # return jira

    # You can add a quick verification call right here to see if it fails early.
    # This helps isolate the problem.
    try:
        server_info = jira.server_info()
        print(
            "Successfully authenticated with Jira. Server version:",
            server_info.get("version"),
        )
    except Exception as e:
        print("!!! FAILED to authenticate with Jira during client creation.")
        raise e

    return jira
    # 3. REMOVE the unnecessary manual header updates and test calls.
    # The 'atlassian-python-api' library handles this automatically.

    print(f"Created Jira client for URL: {JIRA_URL}")
    return jira  # 4. RETURN the powerful, easy-to-use Jira client
