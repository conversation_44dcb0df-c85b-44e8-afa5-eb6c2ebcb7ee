# Jira-MCP Server 🚀

A Message-based Communication Protocol (MCP) server for powerful and secure Jira integration. This project allows an AI agent or any MCP client to manage Jira issues programmatically using a simple set of tools.

## ✨ Tools Available

This MCP server exposes the core Jira issue management functions as callable tools:

- **`create_jira_issue`**: Create a new issue in a specified Jira project with a summary, description, and issue type.
- **`search_issues`**: Find issues using the powerful Jira Query Language (JQL). Search by status, assignee, text content, and more.
- **`update_issue`**: Modify the fields of an existing issue, such as its summary or description.
- **`delete_issue`**: Permanently delete a Jira issue using its key.

## ⚙️ Setup & Configuration

Follow these steps to get the server running locally.

### 1. Prerequisites

- Python 3.9+
- `uv` (or `pip`) installed.
- A Jira Cloud account and an API Token.

### 2. Clone the Repository

```bash
git clone https://gitlab.rapidinnovation.tech/mcp-server/jira-mcp
cd jira-mcp
```

## Setup

1. Create and activate a virtual environment:
   ```sh
   uv venv .venv
   source .venv/bin/activate
   ```
2. Install dependencies:
   ```sh
   uv pip install -r requirements.txt
   ```

## Usage

- Start the server:

  ```sh
  python src/server.py

  or

  uv run src/server.py
  ```

## Folder Structure

- `src/` - Source code
- `src/constants/` - Enums and schemas
- `src/helper/` - Config and logger
- `src/services/` - Jira provider logic

## Requirements

See `requirements.txt` and `pyproject.toml` for details.
